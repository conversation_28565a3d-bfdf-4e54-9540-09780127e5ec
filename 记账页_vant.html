<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>记账</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@4/lib/index.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@vant/icons@latest/font/index.css">
  <style>
    body {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      margin: 0;
      font-family: 'Inter', 'PingFang SC', 'Roboto', Arial, sans-serif;
    }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 395px; height: 832px; background: #000; border-radius: 32px; box-shadow: 0 8px 32px rgba(0,0,0,0.18); display: flex; align-items: center; justify-content: center; margin: 0 auto; }
    .phone { width: 375px; height: 812px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 24px; overflow: hidden; position: relative; }
    .vant-root { min-height: 812px; background: transparent; }
    .topbar {
      height: 64px; background: rgba(255,255,255,0.9); display: flex; align-items: center; justify-content: center; border-bottom: 1px solid rgba(0,0,0,0.06); position: relative; backdrop-filter: blur(10px);
    }
    .topbar-title {
      font-size: 22px; color: #2d3748; font-weight: 700; letter-spacing: 1px;
    }
    /* 美化Tab */
    .custom-tab {
      display: flex;
      justify-content: center;
      margin: 0 auto 0 auto;
      width: 100%;
    }
    .custom-tab .van-tabs__wrap {
      background: #fff !important;
      border-bottom: none;
      margin-bottom: 0;
      justify-content: center;
      display: flex;
    }
    .custom-tab .van-tab {
      border-radius: 18px 18px 0 0;
      margin: 0 12px;
      padding: 0 24px;
      min-width: 80px;
      height: 40px;
      line-height: 40px;
      font-size: 18px;
      transition: background 0.2s, color 0.2s, box-shadow 0.2s;
      text-align: center;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
    .custom-tab .tab-active {
      background: #FFD84C !important;
      color: #222 !important;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(255,216,76,0.10);
      position: relative;
      z-index: 2;
    }
    .custom-tab .tab-inactive {
      background: #f7f7fa !important;
      color: #999 !important;
      font-weight: 500;
    }
    .category-section {
      margin: 18px 18px 0 18px;
    }
    .category-grid {
      display: grid; grid-template-columns: repeat(4, 1fr); gap: 18px 0; justify-items: center;
    }
    .cat-btn {
      display: flex; flex-direction: column; align-items: center; justify-content: flex-start; cursor: pointer; user-select: none; transition: all 0.18s cubic-bezier(.4,2,.6,1);
    }
    .cat-icon-wrap {
      width: 48px; height: 48px; border-radius: 50%; background: #f5f7fa; display: flex; align-items: center; justify-content: center; margin-bottom: 4px; font-size: 28px; color: #b0b0b0; border: 2px solid transparent; transition: all 0.18s cubic-bezier(.4,2,.6,1);
    }
    .cat-btn.selected .cat-icon-wrap {
      border: 2px solid #FFD84C;
      color: #FFD84C;
      background: #fff;
      box-shadow: 0 2px 12px rgba(255,216,76,0.10);
    }
    .cat-label {
      font-size: 14px; color: #718096; margin-top: 2px; transition: color 0.18s;
    }
    .cat-btn.selected .cat-label {
      color: #FFD84C; font-weight: bold;
    }
    /* 软键盘弹窗美化 */
    .custom-keyboard-popup {
      min-height: 420px;
      z-index: 9999 !important;
      border-radius: 24px 24px 0 0;
      box-shadow: 0 -4px 32px rgba(0,0,0,0.10);
      animation: popupIn 0.25s cubic-bezier(.4,2,.6,1);
    }
    @keyframes popupIn {
      0% { transform: translateY(100%); opacity: 0; }
      100% { transform: translateY(0); opacity: 1; }
    }
    .amount-section {
      text-align: center; margin: 18px 0 0 0;
    }
    .amount-value {
      font-size: 44px; font-weight: bold; color: #222; background: rgba(255,255,255,0.95); border-radius: 20px; box-shadow: 0 2px 12px rgba(102,126,234,0.08); padding: 18px 0; margin: 0 auto 8px auto; width: 80%; cursor: pointer; transition: box-shadow 0.2s;
      letter-spacing: 2px; text-align: center; display: flex; align-items: center; justify-content: center;
    }
    .amount-value:active { box-shadow: 0 4px 24px rgba(102,126,234,0.18); }
    .card-row {
      display: flex; justify-content: space-between; align-items: center; margin: 18px 18px 0 18px;
    }
    .info-card {
      flex: 1; background: rgba(255,255,255,0.9); border-radius: 14px; box-shadow: 0 2px 12px rgba(0,0,0,0.06); padding: 10px 0; margin: 0 6px; display: flex; flex-direction: column; align-items: center; cursor: pointer; border: 1px solid rgba(255,255,255,0.2); backdrop-filter: blur(10px); transition: box-shadow 0.18s;
    }
    .info-card-label { font-size: 12px; color: #718096; }
    .info-card-value { font-size: 15px; color: #2d3748; font-weight: 600; margin-top: 2px; }
    .save-btn {
      position: fixed; left: 0; right: 0; bottom: 80px; margin: 0 24px; border-radius: 24px; font-size: 18px; box-shadow: 0 4px 24px rgba(102,126,234,0.12);
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: #fff; font-weight: 700; height: 48px;
      z-index: 100;
    }
    .van-tabbar { 
      position: fixed; left: 0; right: 0; bottom: 0; width: 100%; border-radius: 0; background: rgba(255,255,255,0.95); box-shadow: 0 -2px 10px rgba(0,0,0,0.1); backdrop-filter: blur(10px); border-top: 1px solid rgba(255,255,255,0.2); z-index: 1000;
    }
    .van-tabbar-item {
      color: #718096 !important; font-size: 14px; font-weight: 500;
    }
    .van-tabbar-item--active {
      color: #667eea !important; font-weight: 600;
    }
    .van-number-keyboard { border-radius: 20px 20px 0 0; }
    .van-number-keyboard__wrapper .van-key__wrapper:last-child .van-key--confirm {
      background: linear-gradient(90deg, #FFD84C 0%, #FFE066 100%) !important;
      color: #222 !important;
      font-weight: bold;
      font-size: 18px;
      border-radius: 12px;
    }
    .van-number-keyboard__wrapper .van-key__wrapper .van-key--extra {
      font-size: 16px;
      color: #222;
      font-weight: 600;
    }
    .amount-value {
      margin-bottom: 18px !important;
    }
    .van-popup .van-field, .van-popup .van-button[icon] {
      margin-bottom: 8px;
    }
    .custom-keyboard {
      width: 100%;
      margin: 0 auto;
      padding: 0 0 8px 0;
      background: #f7f7fa;
      border-radius: 18px 18px 0 0;
      box-shadow: 0 -2px 16px rgba(0,0,0,0.06);
      user-select: none;
    }
    .keyboard-row {
      display: flex;
      width: 100%;
      margin-bottom: 8px;
    }
    .key {
      flex: 1;
      margin: 0 6px;
      height: 48px;
      background: #fff;
      border: none;
      border-radius: 12px;
      font-size: 20px;
      color: #222;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      transition: background 0.15s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      cursor: pointer;
    }
    .key:active {
      background: #f0e9d2;
    }
    button.key.key-confirm {
      background: #667eea !important;
      color: #ffffff !important;
      font-weight: bold !important;
      font-size: 18px !important;
      border-radius: 12px !important;
      text-shadow: none !important;
      border: none !important;
      box-shadow: 0 2px 8px rgba(102,126,234,0.3) !important;
      position: relative !important;
      overflow: visible !important;
      height: 48px !important;
    }
    button.key.key-confirm span {
      color: #ffffff !important;
      font-weight: bold !important;
      font-size: 18px !important;
      text-shadow: none !important;
      display: block !important;
    }
    button.key.key-confirm:hover {
      background: #5a6fd8 !important;
      color: #ffffff !important;
      transform: translateY(-1px);
    }
    button.key.key-confirm:active {
      background: #4e5bc7 !important;
      color: #ffffff !important;
      transform: translateY(0);
    }
    .key:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    button.key.key-confirm:disabled {
      background: #e2e8f0 !important;
      color: #a0aec0 !important;
      opacity: 1 !important;
      cursor: not-allowed !important;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
      font-weight: normal !important;
    }
    button.key.key-confirm:disabled span {
      color: #a0aec0 !important;
      font-weight: normal !important;
    }
    button.key.key-confirm:disabled:hover {
      transform: none !important;
    }
    /* 统一tab风格为白色圆角卡片+主色下划线 */
    .tab-card-bg {
      background: #fff;
      border-radius: 18px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.04);
      padding: 0 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: fit-content;
      margin: 18px auto 12px auto;
    }
    .van-tabs__wrap {
      background: transparent !important;
      border-bottom: none;
      box-shadow: none;
      justify-content: center;
      display: flex;
      margin-bottom: 0;
      padding-top: 0;
    }
    .van-tab {
      margin: 0 8px;
      padding: 0 18px;
      min-width: 64px;
      height: 40px;
      line-height: 40px;
      font-size: 18px;
      border-radius: 12px;
      background: transparent;
      color: #999;
      font-weight: 500;
      transition: color 0.2s;
      box-shadow: none;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      position: relative;
    }
    .van-tab--active {
      color: #222 !important;
      font-weight: bold;
    }
    .van-tabs__line {
      background: #FFD84C !important;
      height: 4px !important;
      border-radius: 2px;
      bottom: 4px !important;
    }
  </style>
</head>
<body>
  <div class="mockup">
    <div class="phone">
      <div id="app"></div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
  <script>
    const { createApp, ref, computed } = Vue;
    const { Tab, Tabs, Popup, Button, Icon, NumberKeyboard, DatetimePicker, Field } = vant;
    createApp({
      setup() {
        // 支出/收入Tab
        const tabType = ref('支出');
        const tabList = ['支出', '收入'];
        // 分类数据
        const categoriesMap = {
          '支出': [
            { name: '汽车', icon: 'car-o' },
            { name: '医疗', icon: 'medal-o' },
            { name: '书籍', icon: 'notes-o' },
            { name: '学习', icon: 'edit' },
            { name: '宠物', icon: 'pet-o' },
            { name: '礼金', icon: 'red-packet' },
            { name: '礼物', icon: 'gift-o' },
            { name: '办公', icon: 'bag-o' },
            { name: '维修', icon: 'setting-o' },
            { name: '捐赠', icon: 'like-o' },
            { name: '彩票', icon: 'coupon-o' },
            { name: '亲友', icon: 'friends-o' },
            { name: '快递', icon: 'logistics' },
            { name: '设置', icon: 'setting' },
          ],
          '收入': [
            { name: '工资', icon: 'gold-coin-o' },
            { name: '理财', icon: 'balance-o' },
            { name: '红包', icon: 'red-packet' },
            { name: '兼职', icon: 'cash-back-record-o' },
            { name: '奖金', icon: 'award-o' },
            { name: '其他', icon: 'ellipsis' },
          ]
        };
        const selectedCat = ref(categoriesMap['支出'][0]);
        const categories = computed(() => categoriesMap[tabType.value]);
        // 软键盘弹窗
        const showKeyboard = ref(false);
        // 金额、备注、时间
        const amount = ref('');
        const remarkInput = ref('');
        const dateObj = ref(new Date());
        const showDatePicker = ref(false);
        const datePickerType = ref('datetime'); // 日期时间选择器类型
        // 金额输入表达式
        const amountExpr = ref('');
        // Tab切换时重置分类
        const onTabChange = (name) => {
          tabType.value = name;
          selectedCat.value = categoriesMap[name][0];
        };
        // 分类点击弹出软键盘
        const openKeyboard = (cat) => {
          selectedCat.value = cat;
          showKeyboard.value = true;
        };
        // 精确计算函数，避免浮点数精度问题
        const preciseCalculate = (expression) => {
          try {
            // 将表达式分解为数字和操作符
            const tokens = expression.match(/(\d+\.?\d*|[+\-])/g);
            if (!tokens || tokens.length === 0) return 0;

            let result = parseFloat(tokens[0]) || 0;

            for (let i = 1; i < tokens.length; i += 2) {
              const operator = tokens[i];
              const operand = parseFloat(tokens[i + 1]) || 0;

              if (operator === '+') {
                // 转换为整数计算，避免精度问题
                const factor = Math.max(
                  (result.toString().split('.')[1] || '').length,
                  (operand.toString().split('.')[1] || '').length
                );
                const multiplier = Math.pow(10, factor);
                result = (Math.round(result * multiplier) + Math.round(operand * multiplier)) / multiplier;
              } else if (operator === '-') {
                const factor = Math.max(
                  (result.toString().split('.')[1] || '').length,
                  (operand.toString().split('.')[1] || '').length
                );
                const multiplier = Math.pow(10, factor);
                result = (Math.round(result * multiplier) - Math.round(operand * multiplier)) / multiplier;
              }
            }

            return result;
          } catch {
            return 0;
          }
        };

        // 金额输入
        const onAmountInput = (val) => {
          if (val === '+') {
            if (amountExpr.value && !/[+\-]$/.test(amountExpr.value)) amountExpr.value += '+';
          } else if (val === '-') {
            if (amountExpr.value && !/[+\-]$/.test(amountExpr.value)) amountExpr.value += '-';
          } else if (val === 'delete') {
            amountExpr.value = amountExpr.value.slice(0, -1);
          } else if (val === '.') {
            // 只允许每个数字段有一个小数点
            const parts = amountExpr.value.split(/[+\-]/);
            const last = parts[parts.length - 1];
            if (last && !last.includes('.')) amountExpr.value += val;
          } else if (/\d/.test(val)) {
            amountExpr.value += val;
          }
          // 实时预览结果 - 使用精确计算
          const res = preciseCalculate(amountExpr.value);
          amount.value = res ? res.toFixed(2) : '';
        };
        // 新增删除键处理
        const onDelete = () => {
          amountExpr.value = amountExpr.value.slice(0, -1);
          // 使用精确计算
          const res = preciseCalculate(amountExpr.value);
          amount.value = res ? res.toFixed(2) : '';
        };
        // 新增"今天"按钮逻辑，弹出日期时间选择器
        const onToday = () => {
          showDatePicker.value = true;
        };

        // 确认日期时间选择
        const onDateConfirm = (value) => {
          dateObj.value = value;
          showDatePicker.value = false;
        };

        // 取消日期时间选择
        const onDateCancel = () => {
          showDatePicker.value = false;
        };

        // 日期时间显示优化
        const date = computed(() => {
          const today = new Date();
          const selectedDate = dateObj.value;

          // 判断是否为今天
          const isToday = selectedDate.toDateString() === today.toDateString();

          if (isToday) {
            // 如果是今天，显示"今天 HH:MM"
            const timeStr = selectedDate.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
            return `今天 ${timeStr}`;
          } else {
            // 如果不是今天，显示"MM-DD HH:MM"
            const dateStr = selectedDate.toLocaleDateString('zh-CN', {
              month: '2-digit',
              day: '2-digit'
            });
            const timeStr = selectedDate.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
            return `${dateStr} ${timeStr}`;
          }
        });
        // 记账保存
        const loading = ref(false);
        const onSave = () => {
          // 使用精确计算
          const res = preciseCalculate(amountExpr.value);
          amount.value = res ? res.toFixed(2) : '';

          if (!amount.value || parseFloat(amount.value) <= 0 || !selectedCat.value) {
            vant.showToast({ message: '请填写有效金额和选择分类', type: 'fail' });
            return;
          }
          loading.value = true;
          setTimeout(() => {
            loading.value = false;
            showKeyboard.value = false;
            amount.value = '';
            amountExpr.value = '';
            remarkInput.value = '';
            vant.showToast({ message: '记账成功', type: 'success', onClose: () => window.location.href = '首页_vant.html' });
          }, 1000);
        };
        return {
          tabType, tabList, onTabChange,
          categories, selectedCat, openKeyboard,
          showKeyboard, amount, onAmountInput,
          remarkInput, date, dateObj, showDatePicker,
          loading, onSave, onDelete, onToday,
          amountExpr, onDateConfirm, onDateCancel
        };
      },
      components: {
        'van-tabs': Tabs,
        'van-tab': Tab,
        'van-popup': Popup,
        'van-button': Button,
        'van-icon': Icon,
        'van-number-keyboard': NumberKeyboard,
        'van-datetime-picker': DatetimePicker,
        'van-field': Field
      },
      template: `
        <div class='vant-root'>
          <div class='topbar'>
            <div class='topbar-title'>记一笔</div>
          </div>
          <div class="tab-card-bg">
            <van-tabs v-model:active="tabType" @change="onTabChange" color="#FFD84C" line-width="36" line-height="4" shrink style="margin-bottom:0;">
              <van-tab v-for="tab in tabList" :title="tab" :name="tab" :key="tab" :class="tabType===tab ? 'tab-active' : 'tab-inactive'"></van-tab>
            </van-tabs>
          </div>
          <div class='category-section'>
            <div class='category-grid'>
              <div v-for="cat in categories" :key="cat.name" class="cat-btn" :class="{selected: selectedCat.name === cat.name}" @click.stop="openKeyboard(cat)">
                <div class='cat-icon-wrap'>
                  <van-icon :name="cat.icon" />
                </div>
                <div class='cat-label'>{{cat.name}}</div>
              </div>
            </div>
          </div>
          <van-popup v-model:show="showKeyboard" position="bottom" round class="custom-keyboard-popup" :style="{zIndex: 99999}">
            <div style="text-align:center;padding:18px 0 0 0;">
              <van-icon :name="selectedCat.icon" size="32" style="color:#FFD84C;" />
              <div style="font-size:18px;font-weight:600;margin-top:4px;">{{selectedCat.name}}</div>
            </div>
            <div class="amount-value" style="margin:16px auto 0 auto;width:80%;" @click="showKeyboard = true">
              {{ amountExpr || '0.00' }}
            </div>
            <div style="display:flex;align-items:center;gap:8px;margin:16px 18px 0 18px;">
              <van-field v-model="remarkInput" placeholder="备注" style="flex:1;background:#f7f7fa;border-radius:8px;" />
            </div>
            <!-- 自定义软键盘 -->
            <div class="custom-keyboard">
              <div class="keyboard-row">
                <button class="key" @click="onAmountInput('7')">7</button>
                <button class="key" @click="onAmountInput('8')">8</button>
                <button class="key" @click="onAmountInput('9')">9</button>
                <button class="key" @click="onToday">
                  <van-icon name="calendar-o" style="font-size:20px;vertical-align:middle;" />
                  <span style="font-size:14px;vertical-align:middle;">今天</span>
                </button>
              </div>
              <div class="keyboard-row">
                <button class="key" @click="onAmountInput('4')">4</button>
                <button class="key" @click="onAmountInput('5')">5</button>
                <button class="key" @click="onAmountInput('6')">6</button>
                <button class="key" @click="onAmountInput('+')">+</button>
              </div>
              <div class="keyboard-row">
                <button class="key" @click="onAmountInput('1')">1</button>
                <button class="key" @click="onAmountInput('2')">2</button>
                <button class="key" @click="onAmountInput('3')">3</button>
                <button class="key" @click="onAmountInput('-')">-</button>
              </div>
              <div class="keyboard-row">
                <button class="key" @click="onAmountInput('.')">.</button>
                <button class="key" @click="onAmountInput('0')">0</button>
                <button class="key" @click="onDelete">
                  <van-icon name="delete" style="font-size:20px;" />
                </button>
              </div>
              <div class="keyboard-row">
                <button class="key key-confirm" :disabled="!amount || !selectedCat" @click="onSave" style="flex: 4; margin: 0 6px; background: #667eea !important; color: #ffffff !important;">
                  完成
                </button>
              </div>
            </div>
            <van-popup v-model:show="showDatePicker" position="bottom" round>
              <van-datetime-picker
                type="datetime"
                v-model="dateObj"
                title="选择日期时间"
                @confirm="onDateConfirm"
                @cancel="onDateCancel"
              />
            </van-popup>
          </van-popup>
        </div>
      `
    }).mount('#app');
  </script>
</body>
</html> 
