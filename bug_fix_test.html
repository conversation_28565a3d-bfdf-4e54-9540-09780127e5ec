<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bug修复测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .test-section {
      background: white;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .test-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    .test-case {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .test-result {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
    }
    .success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .layout-test {
      display: flex;
      gap: 16px;
      margin: 10px 0;
    }
    .card {
      flex: 1;
      background: rgba(255,255,255,0.95);
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.08);
      padding: 18px 14px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      border: 1px solid rgba(255,255,255,0.2);
      min-width: 0;
    }
    .card-label {
      font-size: 14px;
      color: #718096;
      font-weight: 500;
    }
    .card-value {
      font-size: 22px;
      font-weight: 700;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
    .card-value.expense { color: #e53e3e; }
    .card-value.income { color: #38a169; }
    .calculator {
      background: white;
      border-radius: 8px;
      padding: 20px;
      max-width: 300px;
    }
    .display {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      font-size: 24px;
      text-align: right;
      margin-bottom: 15px;
      min-height: 40px;
    }
    .keypad {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
    }
    .key {
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 15px;
      font-size: 16px;
      cursor: pointer;
    }
    .key:hover {
      background: #0056b3;
    }
    .key.operator {
      background: #28a745;
    }
    .key.clear {
      background: #dc3545;
    }
  </style>
</head>
<body>
  <h1>Bug修复测试页面</h1>
  
  <!-- 测试1: 布局问题修复 -->
  <div class="test-section">
    <div class="test-title">测试1: 长金额布局问题修复</div>
    
    <h4>修复前的问题：</h4>
    <p>当金额很长时（如 ￥999,999,999.99），会把卡片布局挤变形</p>
    
    <h4>修复后效果：</h4>
    <div class="layout-test">
      <div class="card">
        <div class="card-label">本月支出</div>
        <div class="card-value expense">-￥999,999,999.99</div>
        <div style="font-size:13px;color:#718096;">上月: ￥888,888.88</div>
      </div>
      <div class="card">
        <div class="card-label">本月收入</div>
        <div class="card-value income">+￥1,234,567,890.12</div>
        <div style="font-size:13px;color:#718096;">上月: ￥1,000,000.00</div>
      </div>
    </div>
    
    <div class="test-result success">
      ✅ 修复成功：长金额会自动截断显示，不会破坏布局
    </div>
  </div>

  <!-- 测试2: 计算精度问题修复 -->
  <div class="test-section">
    <div class="test-title">测试2: 计算精度问题修复</div>
    
    <h4>修复前的问题：</h4>
    <p>JavaScript浮点数计算会出现精度问题，如：0.1 + 0.2 = 0.30000000000000004</p>
    
    <h4>测试计算器：</h4>
    <div class="calculator">
      <div class="display" id="display">0.00</div>
      <div class="keypad">
        <button class="key clear" onclick="clearDisplay()">C</button>
        <button class="key" onclick="inputNumber('/')">/</button>
        <button class="key" onclick="inputNumber('*')">×</button>
        <button class="key" onclick="deleteLast()">⌫</button>
        
        <button class="key" onclick="inputNumber('7')">7</button>
        <button class="key" onclick="inputNumber('8')">8</button>
        <button class="key" onclick="inputNumber('9')">9</button>
        <button class="key operator" onclick="inputNumber('-')">-</button>
        
        <button class="key" onclick="inputNumber('4')">4</button>
        <button class="key" onclick="inputNumber('5')">5</button>
        <button class="key" onclick="inputNumber('6')">6</button>
        <button class="key operator" onclick="inputNumber('+')">+</button>
        
        <button class="key" onclick="inputNumber('1')">1</button>
        <button class="key" onclick="inputNumber('2')">2</button>
        <button class="key" onclick="inputNumber('3')">3</button>
        <button class="key" onclick="calculate()" rowspan="2">=</button>
        
        <button class="key" onclick="inputNumber('0')" colspan="2">0</button>
        <button class="key" onclick="inputNumber('.')">.</button>
      </div>
    </div>
    
    <h4>测试用例：</h4>
    <div class="test-case">
      <strong>测试 0.1 + 0.2：</strong>
      <button onclick="testCase('0.1+0.2')">点击测试</button>
      <div id="test1-result"></div>
    </div>
    
    <div class="test-case">
      <strong>测试 1.1 + 2.2：</strong>
      <button onclick="testCase('1.1****')">点击测试</button>
      <div id="test2-result"></div>
    </div>
    
    <div class="test-case">
      <strong>测试 10.1 - 9.9：</strong>
      <button onclick="testCase('10.1-9.9')">点击测试</button>
      <div id="test3-result"></div>
    </div>
  </div>

  <script>
    let currentExpression = '';
    
    // 精确计算函数（修复后的版本）
    function preciseCalculate(expression) {
      try {
        const tokens = expression.match(/(\d+\.?\d*|[+\-])/g);
        if (!tokens || tokens.length === 0) return 0;
        
        let result = parseFloat(tokens[0]) || 0;
        
        for (let i = 1; i < tokens.length; i += 2) {
          const operator = tokens[i];
          const operand = parseFloat(tokens[i + 1]) || 0;
          
          if (operator === '+') {
            const factor = Math.max(
              (result.toString().split('.')[1] || '').length,
              (operand.toString().split('.')[1] || '').length
            );
            const multiplier = Math.pow(10, factor);
            result = (Math.round(result * multiplier) + Math.round(operand * multiplier)) / multiplier;
          } else if (operator === '-') {
            const factor = Math.max(
              (result.toString().split('.')[1] || '').length,
              (operand.toString().split('.')[1] || '').length
            );
            const multiplier = Math.pow(10, factor);
            result = (Math.round(result * multiplier) - Math.round(operand * multiplier)) / multiplier;
          }
        }
        
        return result;
      } catch {
        return 0;
      }
    }
    
    function inputNumber(value) {
      if (value === '+' || value === '-') {
        if (currentExpression && !/[+\-]$/.test(currentExpression)) {
          currentExpression += value;
        }
      } else if (value === '.') {
        const parts = currentExpression.split(/[+\-]/);
        const last = parts[parts.length - 1];
        if (last && !last.includes('.')) {
          currentExpression += value;
        }
      } else {
        currentExpression += value;
      }
      updateDisplay();
    }
    
    function updateDisplay() {
      const result = preciseCalculate(currentExpression);
      document.getElementById('display').textContent = currentExpression || '0.00';
    }
    
    function calculate() {
      const result = preciseCalculate(currentExpression);
      document.getElementById('display').textContent = result.toFixed(2);
      currentExpression = result.toString();
    }
    
    function clearDisplay() {
      currentExpression = '';
      document.getElementById('display').textContent = '0.00';
    }
    
    function deleteLast() {
      currentExpression = currentExpression.slice(0, -1);
      updateDisplay();
    }
    
    function testCase(expression) {
      const oldResult = eval(expression);
      const newResult = preciseCalculate(expression);
      
      let resultDiv;
      if (expression === '0.1+0.2') resultDiv = document.getElementById('test1-result');
      else if (expression === '1.1****') resultDiv = document.getElementById('test2-result');
      else if (expression === '10.1-9.9') resultDiv = document.getElementById('test3-result');
      
      resultDiv.innerHTML = `
        <div style="margin-top: 8px;">
          <div>原始计算结果: ${oldResult}</div>
          <div>修复后结果: ${newResult.toFixed(2)}</div>
          <div class="${newResult.toFixed(2) === Math.round(newResult * 100) / 100 ? 'success' : 'error'}" style="margin-top: 4px; padding: 4px; border-radius: 4px;">
            ${newResult.toFixed(2) === (Math.round(newResult * 100) / 100).toFixed(2) ? '✅ 精度正确' : '❌ 仍有精度问题'}
          </div>
        </div>
      `;
    }
  </script>
</body>
</html>
