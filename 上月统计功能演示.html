<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>上月账单统计功能演示</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
    
    .demo-container {
      max-width: 375px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .demo-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      text-align: center;
    }
    
    .demo-content {
      padding: 20px;
    }
    
    /* 上月账单统计样式 */
    .last-month-section {
      margin-bottom: 20px;
    }
    
    .last-month-card {
      background: rgba(255,255,255,0.95);
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      padding: 20px 18px;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .last-month-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    
    .last-month-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
    }
    
    .last-month-detail-btn {
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      color: #ffffff;
      font-size: 12px;
      padding: 6px 12px;
      border-radius: 12px;
      font-weight: 500;
      border: none;
      cursor: pointer;
    }
    
    .last-month-overview {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .overview-item {
      flex: 1;
      border-radius: 12px;
      padding: 12px;
      border: 1px solid;
    }
    
    .overview-item.expense {
      background: rgba(229,62,62,0.05);
      border-color: rgba(229,62,62,0.1);
    }
    
    .overview-item.income {
      background: rgba(56,161,105,0.05);
      border-color: rgba(56,161,105,0.1);
    }
    
    .overview-item.balance {
      background: rgba(102,126,234,0.05);
      border-color: rgba(102,126,234,0.1);
    }
    
    .overview-label {
      display: block;
      font-size: 12px;
      margin-bottom: 4px;
    }
    
    .overview-item.expense .overview-label {
      color: #e53e3e;
    }
    
    .overview-item.income .overview-label {
      color: #38a169;
    }
    
    .overview-item.balance .overview-label {
      color: #667eea;
    }
    
    .overview-value {
      font-size: 18px;
      font-weight: 700;
    }
    
    .overview-item.expense .overview-value {
      color: #e53e3e;
    }
    
    .overview-item.income .overview-value {
      color: #38a169;
    }
    
    .last-month-categories {
      margin-bottom: 16px;
    }
    
    .categories-title {
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }
    
    .categories-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .category-item {
      background: rgba(102,126,234,0.05);
      border-radius: 8px;
      padding: 6px 10px;
      border: 1px solid rgba(102,126,234,0.1);
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .category-name {
      font-size: 12px;
      color: #667eea;
      font-weight: 500;
    }
    
    .category-amount {
      font-size: 12px;
      color: #718096;
    }
    
    .last-month-stats {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid rgba(0,0,0,0.05);
    }
    
    .stat-item {
      font-size: 13px;
      color: #718096;
    }
    
    .feature-list {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      margin-top: 20px;
    }
    
    .feature-list h3 {
      margin: 0 0 12px 0;
      color: #2d3748;
      font-size: 16px;
    }
    
    .feature-list ul {
      margin: 0;
      padding-left: 20px;
      color: #4a5568;
      line-height: 1.6;
    }
    
    .feature-list li {
      margin-bottom: 8px;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <div class="demo-header">
      <h1 style="margin: 0; font-size: 20px;">上月账单统计功能</h1>
      <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">首页新增功能演示</p>
    </div>
    
    <div class="demo-content">
      <!-- 上月账单统计区 -->
      <div class="last-month-section">
        <div class="last-month-card">
          <div class="last-month-header">
            <div class="last-month-title">
              <span>📅</span>
              <span>上月账单统计</span>
            </div>
            <button class="last-month-detail-btn" onclick="showDetails()">查看详情</button>
          </div>
          
          <!-- 上月收支概览 -->
          <div class="last-month-overview">
            <div class="overview-item expense">
              <span class="overview-label">支出</span>
              <span class="overview-value">￥2,800</span>
            </div>
            <div class="overview-item income">
              <span class="overview-label">收入</span>
              <span class="overview-value">￥4,800</span>
            </div>
            <div class="overview-item balance">
              <span class="overview-label">结余</span>
              <span class="overview-value" style="color: #38a169;">￥2,000</span>
            </div>
          </div>
          
          <!-- 上月分类统计 -->
          <div class="last-month-categories">
            <div class="categories-title">主要支出分类</div>
            <div class="categories-list">
              <div class="category-item">
                <span class="category-name">餐饮</span>
                <span class="category-amount">￥850</span>
              </div>
              <div class="category-item">
                <span class="category-name">购物</span>
                <span class="category-amount">￥600</span>
              </div>
              <div class="category-item">
                <span class="category-name">交通</span>
                <span class="category-amount">￥320</span>
              </div>
            </div>
          </div>
          
          <!-- 上月统计信息 -->
          <div class="last-month-stats">
            <span class="stat-item">账单数量：15笔</span>
            <span class="stat-item">日均支出：￥93</span>
          </div>
        </div>
      </div>
      
      <!-- 功能说明 -->
      <div class="feature-list">
        <h3>✨ 新增功能特点</h3>
        <ul>
          <li><strong>自动计算</strong> - 基于历史账单数据自动计算上月统计</li>
          <li><strong>收支概览</strong> - 清晰展示上月收入、支出和结余</li>
          <li><strong>分类统计</strong> - 显示主要支出分类和金额</li>
          <li><strong>详细信息</strong> - 账单数量、日均支出等统计指标</li>
          <li><strong>快速查看</strong> - 点击"查看详情"可跳转到完整账单</li>
          <li><strong>响应式设计</strong> - 适配不同屏幕尺寸</li>
          <li><strong>保持原功能</strong> - 不影响现有的任何功能</li>
        </ul>
      </div>
      
      <div class="feature-list">
        <h3>🔧 技术实现</h3>
        <ul>
          <li><strong>HTML版本</strong> - 在首页模板中添加上月统计模块</li>
          <li><strong>小程序版本</strong> - 在WXML中添加对应组件</li>
          <li><strong>数据计算</strong> - JavaScript自动筛选和统计上月数据</li>
          <li><strong>样式优化</strong> - 统一的视觉风格和交互效果</li>
          <li><strong>用户体验</strong> - 弹窗详情展示，支持跳转查看</li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    function showDetails() {
      alert(`上月账单详情

统计周期：2024年6月

总收入：￥4,800
总支出：￥2,800
结余：￥2,000
账单数量：15笔
日均支出：￥93

主要支出分类：
• 餐饮：￥850
• 购物：￥600
• 交通：￥320

点击"查看完整账单"可跳转到账单详情页面`);
    }
  </script>
</body>
</html>
