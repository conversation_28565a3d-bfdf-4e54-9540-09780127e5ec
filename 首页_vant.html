<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>首页（现代科技感）</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@4/lib/index.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@vant/icons@latest/font/index.css">
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <style>
    body {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      margin: 0;
      font-family: 'Inter', 'PingFang SC', 'Roboto', Aria<PERSON>, sans-serif;
    }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 395px; height: 832px; background: #000; border-radius: 32px; box-shadow: 0 8px 32px rgba(0,0,0,0.18); display: flex; align-items: center; justify-content: center; margin: 0 auto; }
    .phone { width: 375px; height: 812px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 24px; overflow: hidden; position: relative; }
    .vant-root { min-height: 812px; background: transparent; }
    .topbar {
      height: 64px; background: rgba(255,255,255,0.9); display: flex; align-items: center; justify-content: center; border-bottom: 1px solid rgba(0,0,0,0.06); position: relative; backdrop-filter: blur(10px);
    }
    .topbar-title {
      font-size: 22px; color: #2d3748; font-weight: 700; letter-spacing: 1px;
    }
    .summary-cards {
      display: flex; justify-content: space-around; margin: 24px 18px 16px 18px; gap: 12px;
    }
    .summary-card {
      background: rgba(255,255,255,0.9); border-radius: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); padding: 20px 12px; flex: 1; text-align: center; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);
    }
    .summary-label { color: #718096; font-size: 13px; font-weight: 500; margin-bottom: 8px; }
    .summary-value { font-size: 24px; font-weight: 800; line-height: 1; }
    .summary-card:nth-child(1) .summary-value { color: #e53e3e; }
    .summary-card:nth-child(2) .summary-value { color: #38a169; }
    .summary-card:nth-child(3) .summary-value { color: #3182ce; }
    .quick-btn { margin: 24px 18px 20px 18px; }
    .quick-btn .van-button {
      width: 100%; height: 56px; border-radius: 28px; font-size: 18px; font-weight: 700; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #fff; border: none; box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    }
    .category-section { margin: 0 18px; }
    .category-title {
      color: #2d3748; font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; justify-content: space-between;
    }
    .category-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; }
    .cat-btn { display: flex; flex-direction: column; align-items: center; cursor: pointer; user-select: none; }
    .cat-icon-wrap {
      width: 56px; height: 56px; border-radius: 16px; background: rgba(255,255,255,0.9); display: flex; align-items: center; justify-content: center; margin-bottom: 8px; font-size: 24px; color: #718096; border: 2px solid transparent; transition: all 0.3s ease; backdrop-filter: blur(10px); box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    }
    .cat-btn.selected .cat-icon-wrap { border: 2px solid #667eea; color: #667eea; background: rgba(102, 126, 234, 0.1); }
    .cat-btn.selected .cat-label { color: #667eea; font-weight: 600; }
    .cat-label { font-size: 13px; color: #4a5568; font-weight: 500; }
    .trend-card {
      background: rgba(255,255,255,0.9); border-radius: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); margin: 24px 18px 80px 18px; padding: 20px; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);
    }
    .trend-title { color: #2d3748; font-size: 16px; font-weight: 600; margin-bottom: 16px; }
    .trend-chart-home { width: 100%; height: 140px; }
    .van-tabbar { 
      position: fixed; left: 0; right: 0; bottom: 0; width: 100%; border-radius: 0; background: rgba(255,255,255,0.95); box-shadow: 0 -2px 10px rgba(0,0,0,0.1); backdrop-filter: blur(10px); border-top: 1px solid rgba(255,255,255,0.2); z-index: 1000;
    }
    .van-tabbar-item {
      color: #718096 !important; font-size: 14px; font-weight: 500;
    }
    .van-tabbar-item--active {
      color: #667eea !important; font-weight: 600;
    }
    .setting-icon {
      width: 32px; height: 32px; border-radius: 8px; background: rgba(102, 126, 234, 0.1); display: flex; align-items: center; justify-content: center; color: #667eea; cursor: pointer; transition: all 0.3s ease;
    }
    .setting-icon:hover {
      background: rgba(102, 126, 234, 0.2);
    }
  </style>
</head>
<body>
  <div class="mockup">
    <div class="phone">
      <div id="app"></div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <script>
    const { createApp, ref, onMounted, watch } = Vue;
    const { Tabbar, TabbarItem, Icon, Button, Dialog, Progress, Tabs, Tab } = vant;
    createApp({
      setup() {
        // 预算数据
        const budget = ref(5000);
        const used = ref(5200);
        const remain = ref(budget.value - used.value);
        const overBudget = ref(used.value > budget.value);
        // 新增：预算Dialog相关
        const showBudgetDialog = ref(false);
        const activeTab = ref('month');
        const budgetInput = ref({ month: '', quarter: '', year: '' });
        function openBudgetDialog() { showBudgetDialog.value = true; }
        function saveBudget() {
          budget.value = Number(budgetInput.value[activeTab.value]) || budget.value;
          showBudgetDialog.value = false;
          Dialog.alert({
            title: '保存成功',
            message: `已保存${tabLabel(activeTab.value)}预算：￥${budgetInput.value[activeTab.value]}`,
            theme: 'round-button',
          });
        }
        function tabLabel(tab) {
          if(tab==='month') return '本月';
          if(tab==='quarter') return '本季度';
          if(tab==='year') return '本年';
          return '';
        }
        // 最近账单
        const bills = ref([
          { id: 1, type: '支出', category: '餐饮', amount: 32, date: '2024-07-01', remark: '午餐' },
          { id: 2, type: '收入', category: '工资', amount: 5000, date: '2024-07-01', remark: '发工资' },
          { id: 3, type: '支出', category: '交通', amount: 8, date: '2024-06-30', remark: '地铁' },
        ]);

        // 上月账单数据
        const lastMonthBills = ref([
          { id: 101, type: '支出', category: '餐饮', amount: 450, date: '2024-06-30', remark: '聚餐' },
          { id: 102, type: '支出', category: '购物', amount: 280, date: '2024-06-29', remark: '买衣服' },
          { id: 103, type: '支出', category: '交通', amount: 120, date: '2024-06-28', remark: '打车' },
          { id: 104, type: '收入', category: '工资', amount: 4800, date: '2024-06-25', remark: '月薪' },
          { id: 105, type: '支出', category: '娱乐', amount: 180, date: '2024-06-24', remark: '看电影' },
          { id: 106, type: '支出', category: '餐饮', amount: 85, date: '2024-06-23', remark: '外卖' },
          { id: 107, type: '支出', category: '医疗', amount: 150, date: '2024-06-22', remark: '体检' },
          { id: 108, type: '收入', category: '理财', amount: 200, date: '2024-06-20', remark: '基金收益' },
          { id: 109, type: '支出', category: '购物', amount: 320, date: '2024-06-18', remark: '日用品' },
          { id: 110, type: '支出', category: '餐饮', amount: 95, date: '2024-06-15', remark: '聚餐' },
          { id: 111, type: '支出', category: '交通', amount: 45, date: '2024-06-12', remark: '地铁' },
          { id: 112, type: '支出', category: '娱乐', amount: 220, date: '2024-06-10', remark: 'KTV' },
          { id: 113, type: '支出', category: '餐饮', amount: 65, date: '2024-06-08', remark: '午餐' },
          { id: 114, type: '支出', category: '其他', amount: 80, date: '2024-06-05', remark: '杂费' },
          { id: 115, type: '支出', category: '餐饮', amount: 110, date: '2024-06-03', remark: '晚餐' },
        ]);

        // 计算上月统计数据
        const lastMonthStats = ref({});
        const lastMonthTopCategories = ref([]);

        function calculateLastMonthStats() {
          const expenseBills = lastMonthBills.value.filter(bill => bill.type === '支出');
          const incomeBills = lastMonthBills.value.filter(bill => bill.type === '收入');

          const totalExpense = expenseBills.reduce((sum, bill) => sum + bill.amount, 0);
          const totalIncome = incomeBills.reduce((sum, bill) => sum + bill.amount, 0);
          const balance = totalIncome - totalExpense;
          const dailyAverage = Math.round(totalExpense / 30); // 按30天计算

          // 计算分类统计
          const categoryStats = {};
          expenseBills.forEach(bill => {
            if (categoryStats[bill.category]) {
              categoryStats[bill.category] += bill.amount;
            } else {
              categoryStats[bill.category] = bill.amount;
            }
          });

          // 获取前3个支出分类
          const topCategories = Object.entries(categoryStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([name, amount]) => ({ name, amount }));

          lastMonthStats.value = {
            expense: totalExpense,
            income: totalIncome,
            balance: balance,
            count: lastMonthBills.value.length,
            dailyAverage: dailyAverage
          };

          lastMonthTopCategories.value = topCategories;
        }

        // 查看上月详情
        function viewLastMonthDetails() {
          // 这里可以跳转到账单详情页面，并传递上月的筛选条件
          // 或者显示一个弹窗展示更详细的上月账单
          Dialog.alert({
            title: '上月账单详情',
            message: `
              <div style="text-align: left; line-height: 1.6;">
                <p><strong>统计周期：</strong>2024年6月</p>
                <p><strong>总收入：</strong>￥${lastMonthStats.value.income}</p>
                <p><strong>总支出：</strong>￥${lastMonthStats.value.expense}</p>
                <p><strong>结余：</strong>￥${lastMonthStats.value.balance}</p>
                <p><strong>账单数量：</strong>${lastMonthStats.value.count}笔</p>
                <p><strong>日均支出：</strong>￥${lastMonthStats.value.dailyAverage}</p>
                <br>
                <p><strong>主要支出分类：</strong></p>
                ${lastMonthTopCategories.value.map(cat =>
                  `<p style="margin: 4px 0;">• ${cat.name}：￥${cat.amount}</p>`
                ).join('')}
              </div>
            `,
            theme: 'round-button',
            allowHtml: true,
            confirmButtonText: '查看完整账单',
          }).then(() => {
            // 跳转到账单页面，可以添加筛选参数
            window.location.href = '账单明细页_vant.html?month=2024-06';
          });
        }
        // 分类宫格
        const categories = [
          { name: '餐饮', icon: 'bill-o' },
          { name: '购物', icon: 'cart-o' },
          { name: '娱乐', icon: 'smile-o' },
          { name: '交通', icon: 'guide-o' },
          { name: '工资', icon: 'balance-o' },
          { name: '理财', icon: 'gold-coin-o' },
          { name: '医疗', icon: 'medal-o' },
          { name: '其他', icon: 'apps-o' },
        ];
        const selectedCat = ref('餐饮');
        const selectCat = (name) => { selectedCat.value = name; };
        // 统计Tab
        const statTab = ref('trend');
        // 跳转
        function goPage(page) {
          if(page==='home') window.location.href = '首页_vant.html';
          if(page==='bill') window.location.href = '账单明细页_vant.html';
          if(page==='stat') window.location.href = '统计分析页_vant.html';
          if(page==='mine') window.location.href = '我的_vant.html';
          if(page==='add') window.location.href = '记账页_vant.html';
        }
        // 统计图表渲染
        function renderChart() {
          setTimeout(() => {
            var chartDom = document.getElementById('statChart');
            if(chartDom) {
              var myChart = echarts.init(chartDom);
              var option = {
                backgroundColor: 'transparent',
                grid: { left: 10, right: 10, top: 20, bottom: 20, containLabel: true },
                tooltip: { trigger: 'axis', backgroundColor: 'rgba(255,255,255,0.95)', borderColor: 'rgba(0,0,0,0.1)', textStyle: { color: '#2d3748' } },
                legend: { data: ['支出','收入'], top: 0, right: 0, icon: 'circle', itemWidth: 8, itemHeight: 8, textStyle: { fontSize: 12, color: '#718096' } },
                xAxis: { type: 'category', boundaryGap: false, data: ['周一','周二','周三','周四','周五','周六','周日'], axisLine: { lineStyle: { color: 'rgba(0,0,0,0.1)' } }, axisLabel: { fontSize: 12, color: '#718096' } },
                yAxis: { type: 'value', splitLine: { lineStyle: { color: 'rgba(0,0,0,0.05)' } }, axisLabel: { fontSize: 12, color: '#718096' } },
                series: [
                  { name: '支出', type: 'line', smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { color: '#e53e3e', width: 3 }, itemStyle: { color: '#e53e3e' }, areaStyle: { color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [ { offset: 0, color: 'rgba(229,62,62,0.15)' }, { offset: 1, color: 'rgba(229,62,62,0)' } ] } }, data: [120, 132, 101, 134, 90, 230, 210] },
                  { name: '收入', type: 'line', smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { color: '#38a169', width: 3 }, itemStyle: { color: '#38a169' }, areaStyle: { color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [ { offset: 0, color: 'rgba(56,161,105,0.15)' }, { offset: 1, color: 'rgba(56,161,105,0)' } ] } }, data: [220, 182, 191, 234, 290, 330, 310] }
                ]
              };
              myChart.setOption(option);
            }
          }, 200);
        }
        // 初始化上月统计数据
        onMounted(() => {
          calculateLastMonthStats();
          renderChart();
        });
        watch([selectedCat, statTab], renderChart);
        return {
          budget, used, remain, overBudget, bills, categories, selectedCat, selectCat, statTab, goPage,
          showBudgetDialog, openBudgetDialog, activeTab, budgetInput, saveBudget, tabLabel,
          lastMonthStats, lastMonthTopCategories, viewLastMonthDetails
        };
      },
      components: { 'van-tabbar': Tabbar, 'van-tabbar-item': TabbarItem, 'van-icon': Icon, 'van-button': Button, 'van-dialog': Dialog, 'van-progress': Progress, 'van-tabs': Tabs, 'van-tab': Tab },
      template: `
        <div class="vant-root" style="min-height:812px;position:relative;">
          <div style="overflow-y:auto;height:812px;min-height:812px;padding-bottom:120px;">
            <div style="font-size:22px;font-weight:700;color:#2d3748;letter-spacing:1px;margin:28px 0 0 24px;">简易记账</div>
            <!-- 预算模块 -->
            <div style="margin:28px 18px 0 18px;">
              <div style="background:rgba(255,255,255,0.95);border-radius:20px;box-shadow:0 4px 20px rgba(0,0,0,0.08);padding:20px 18px;display:flex;flex-direction:column;gap:10px;align-items:flex-start;position:relative;">
                <div style="font-size:15px;font-weight:600;color:#2d3748;display:flex;align-items:center;gap:8px;">
                  <van-icon name="balance-o" color="#667eea" size="18" /> 本月预算
                  <span v-if="overBudget" style="color:#e53e3e;font-size:13px;font-weight:600;margin-left:8px;">已超额</span>
                </div>
                <div style="font-size:22px;font-weight:700;color:#667eea;word-break:break-all;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%;">￥{{budget}}</div>
                <div style="width:100%;display:flex;align-items:center;gap:12px;min-width:0;">
                  <span style="font-size:13px;color:#718096;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">已用￥{{used}}</span>
                  <van-progress :percentage="Math.min(100, Math.round(used/budget*100))" :color="overBudget ? '#e53e3e' : 'linear-gradient(90deg,#667eea,#764ba2)'" stroke-width="8" style="flex:1;min-width:60px;" />
                  <span style="font-size:13px;color:#718096;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">剩余￥{{remain>0?remain:0}}</span>
                </div>
                <van-button size="small" round style="margin-top:8px;background:linear-gradient(90deg,#667eea 0%,#764ba2 100%);color:#fff;border:none;" @click="openBudgetDialog">设置预算</van-button>
              </div>
            </div>
            <!-- 预算设置Dialog -->
            <van-dialog v-model:show="showBudgetDialog" title="设置预算" show-cancel-button="false" show-confirm-button="false" style="border-radius:18px;overflow:hidden;">
              <div style="padding:0 0 18px 0;">
                <van-tabs v-model:active="activeTab">
                  <van-tab title="月预算" name="month">
                    <van-field v-model="budgetInput.month" label="本月预算" placeholder="请输入本月预算金额" type="number" clearable />
                  </van-tab>
                  <van-tab title="季度预算" name="quarter">
                    <van-field v-model="budgetInput.quarter" label="本季度预算" placeholder="请输入本季度预算金额" type="number" clearable />
                  </van-tab>
                  <van-tab title="年预算" name="year">
                    <van-field v-model="budgetInput.year" label="本年预算" placeholder="请输入本年预算金额" type="number" clearable />
                  </van-tab>
                </van-tabs>
                <van-button type="primary" round style="width:90%;margin:18px 5% 0 5%;height:44px;font-size:16px;" @click="saveBudget">保存</van-button>
              </div>
            </van-dialog>
            <!-- 当月支出/收入统计区 -->
            <div style="margin:28px 18px 0 18px;display:flex;gap:16px;">
              <div style="flex:1;background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:18px 14px;display:flex;flex-direction:column;align-items:flex-start;gap:8px;border:1px solid rgba(255,255,255,0.2);min-width:0;">
                <div style="font-size:14px;color:#718096;font-weight:500;">本月支出</div>
                <div style="font-size:22px;font-weight:700;color:#e53e3e;word-break:break-all;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%;">-￥3,200</div>
                <div style="font-size:13px;color:#718096;display:flex;align-items:center;gap:4px;width:100%;overflow:hidden;">
                  <span style="white-space:nowrap;">上月: ￥2,800</span>
                  <van-icon name="arrow-up" color="#e53e3e" v-if="3200>2800" />
                  <van-icon name="arrow-down" color="#38a169" v-else />
                  <span :style="3200>2800 ? 'color:#e53e3e;' : 'color:#38a169;'" style="white-space:nowrap;">{{Math.abs(3200-2800)}}</span>
                </div>
              </div>
              <div style="flex:1;background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:18px 14px;display:flex;flex-direction:column;align-items:flex-start;gap:8px;border:1px solid rgba(255,255,255,0.2);min-width:0;">
                <div style="font-size:14px;color:#718096;font-weight:500;">本月收入</div>
                <div style="font-size:22px;font-weight:700;color:#38a169;word-break:break-all;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%;">+￥5,000</div>
                <div style="font-size:13px;color:#718096;display:flex;align-items:center;gap:4px;width:100%;overflow:hidden;">
                  <span style="white-space:nowrap;">上月: ￥4,800</span>
                  <van-icon name="arrow-up" color="#38a169" v-if="5000>4800" />
                  <van-icon name="arrow-down" color="#e53e3e" v-else />
                  <span :style="5000>4800 ? 'color:#38a169;' : 'color:#e53e3e;'" style="white-space:nowrap;">{{Math.abs(5000-4800)}}</span>
                </div>
              </div>
            </div>
            <!-- 上月账单统计区 -->
            <div style="margin:28px 18px 0 18px;">
              <div style="background:rgba(255,255,255,0.95);border-radius:20px;box-shadow:0 4px 20px rgba(0,0,0,0.08);padding:20px 18px;border:1px solid rgba(255,255,255,0.2);">
                <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:16px;">
                  <div style="display:flex;align-items:center;gap:8px;">
                    <van-icon name="calendar-o" color="#667eea" size="18" />
                    <span style="font-size:16px;font-weight:600;color:#2d3748;">上月账单统计</span>
                  </div>
                  <van-button size="mini" round style="background:linear-gradient(90deg,#667eea 0%,#764ba2 100%);color:#fff;border:none;font-size:12px;" @click="viewLastMonthDetails">查看详情</van-button>
                </div>

                <!-- 上月收支概览 -->
                <div style="display:flex;gap:12px;margin-bottom:16px;">
                  <div style="flex:1;background:rgba(229,62,62,0.05);border-radius:12px;padding:12px;border:1px solid rgba(229,62,62,0.1);">
                    <div style="font-size:12px;color:#e53e3e;margin-bottom:4px;">支出</div>
                    <div style="font-size:18px;font-weight:700;color:#e53e3e;">￥{{lastMonthStats.expense}}</div>
                  </div>
                  <div style="flex:1;background:rgba(56,161,105,0.05);border-radius:12px;padding:12px;border:1px solid rgba(56,161,105,0.1);">
                    <div style="font-size:12px;color:#38a169;margin-bottom:4px;">收入</div>
                    <div style="font-size:18px;font-weight:700;color:#38a169;">￥{{lastMonthStats.income}}</div>
                  </div>
                  <div style="flex:1;background:rgba(102,126,234,0.05);border-radius:12px;padding:12px;border:1px solid rgba(102,126,234,0.1);">
                    <div style="font-size:12px;color:#667eea;margin-bottom:4px;">结余</div>
                    <div style="font-size:18px;font-weight:700;" :style="lastMonthStats.balance >= 0 ? 'color:#38a169;' : 'color:#e53e3e;'">￥{{lastMonthStats.balance}}</div>
                  </div>
                </div>

                <!-- 上月分类统计 -->
                <div style="margin-bottom:16px;">
                  <div style="font-size:14px;font-weight:600;color:#2d3748;margin-bottom:8px;">主要支出分类</div>
                  <div style="display:flex;flex-wrap:wrap;gap:8px;">
                    <div v-for="cat in lastMonthTopCategories" :key="cat.name" style="background:rgba(102,126,234,0.05);border-radius:8px;padding:6px 10px;border:1px solid rgba(102,126,234,0.1);">
                      <span style="font-size:12px;color:#667eea;font-weight:500;">{{cat.name}}</span>
                      <span style="font-size:12px;color:#718096;margin-left:4px;">￥{{cat.amount}}</span>
                    </div>
                  </div>
                </div>

                <!-- 上月账单数量 -->
                <div style="display:flex;justify-content:space-between;align-items:center;padding-top:12px;border-top:1px solid rgba(0,0,0,0.05);">
                  <span style="font-size:13px;color:#718096;">账单数量：{{lastMonthStats.count}}笔</span>
                  <span style="font-size:13px;color:#718096;">日均支出：￥{{lastMonthStats.dailyAverage}}</span>
                </div>
              </div>
            </div>

            <!-- 最近账单区 -->
            <div style="margin:28px 18px 0 18px;">
              <div style="font-size:16px;font-weight:600;color:#2d3748;margin-bottom:12px;">最近账单</div>
              <div v-for="item in bills" :key="item.id" style="background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:14px 16px;margin-bottom:12px;display:flex;align-items:center;justify-content:space-between;border:1px solid rgba(255,255,255,0.2);">
                <div style="display:flex;flex-direction:column;gap:2px;">
                  <span style="font-size:15px;font-weight:600;color:#2d3748;">{{item.category}} <span v-if="item.type==='收入'" style="color:#38a169;">收入</span><span v-else style="color:#e53e3e;">支出</span></span>
                  <span style="font-size:13px;color:#888;">{{item.date}} <span v-if="item.remark">· {{item.remark}}</span></span>
                </div>
                <span :style="item.type==='收入' ? 'color:#38a169;font-weight:700;font-size:18px;' : 'color:#e53e3e;font-weight:700;font-size:18px;'">
                  {{item.type==='收入' ? '+' : '-'}}￥{{item.amount}}
                </span>
              </div>
            </div>
          </div>
          <!-- 悬浮记一笔按钮 -->
          <van-button type="primary" icon="edit" round style="position:fixed;right:36px;bottom:110px;width:60px;height:60px;box-shadow:0 4px 16px rgba(102,126,234,0.18);font-size:28px;background:linear-gradient(90deg,#667eea 0%,#764ba2 100%);border:none;z-index:2000;" @click="goPage('add')"></van-button>
          <!-- 底部tab栏 -->
          <van-tabbar>
            <van-tabbar-item icon="home-o" @click="goPage('home')">首页</van-tabbar-item>
            <van-tabbar-item icon="notes-o" @click="goPage('bill')">账单</van-tabbar-item>
            <van-tabbar-item icon="chart-trending-o" @click="goPage('stat')">统计</van-tabbar-item>
            <van-tabbar-item icon="user-o" @click="goPage('mine')">我的</van-tabbar-item>
          </van-tabbar>
        </div>
      `
    }).mount('#app');
  </script>
</body>
</html> 
