<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>完成按钮修复测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .test-section {
      background: white;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .test-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    /* 修复前的样式（有问题的版本） */
    .keyboard-old {
      width: 100%;
      margin: 20px auto;
      padding: 16px;
      background: #f7f7fa;
      border-radius: 18px;
      box-shadow: 0 -2px 16px rgba(0,0,0,0.06);
    }
    .keyboard-row {
      display: flex;
      width: 100%;
      margin-bottom: 8px;
    }
    .key {
      flex: 1;
      margin: 0 6px;
      height: 48px;
      background: #fff;
      border: none;
      border-radius: 12px;
      font-size: 20px;
      color: #222;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      transition: background 0.15s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      cursor: pointer;
    }
    .key:active {
      background: #f0e9d2;
    }
    
    /* 修复前的完成按钮样式（有问题） */
    .key-confirm-old {
      background: linear-gradient(90deg, #FFD84C 0%, #FFE066 100%) !important;
      color: #222 !important;
      font-weight: bold;
      font-size: 18px;
      border-radius: 12px;
    }
    .key-confirm-old:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    /* 修复后的完成按钮样式 */
    .key-confirm-new {
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
      color: #ffffff !important;
      font-weight: bold !important;
      font-size: 16px !important;
      border-radius: 12px !important;
      text-shadow: none !important;
      border: none !important;
      box-shadow: 0 2px 8px rgba(102,126,234,0.3) !important;
      position: relative !important;
      overflow: visible !important;
    }
    .key-confirm-new:hover {
      background: linear-gradient(90deg, #5a6fd8 0%, #6a4190 100%) !important;
      transform: translateY(-1px);
    }
    .key-confirm-new:active {
      background: linear-gradient(90deg, #4e5bc7 0%, #5e377f 100%) !important;
      transform: translateY(0);
    }
    .key-confirm-new:disabled {
      background: #e2e8f0 !important;
      color: #a0aec0 !important;
      opacity: 1 !important;
      cursor: not-allowed !important;
      box-shadow: none !important;
      font-weight: normal !important;
    }
    .key-confirm-new:disabled:hover {
      transform: none !important;
    }
    
    .status-indicator {
      margin: 10px 0;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
    }
    .status-enabled {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status-disabled {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .toggle-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px 5px;
    }
    .toggle-btn:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <h1>完成按钮修复测试</h1>
  
  <!-- 修复前后对比 -->
  <div class="test-section">
    <div class="test-title">修复前后对比</div>
    
    <h4>修复前（有问题的版本）：</h4>
    <div class="keyboard-old">
      <div class="keyboard-row">
        <button class="key">7</button>
        <button class="key">8</button>
        <button class="key">9</button>
        <button class="key">+</button>
      </div>
      <div class="keyboard-row">
        <button class="key">4</button>
        <button class="key">5</button>
        <button class="key">6</button>
        <button class="key">-</button>
      </div>
      <div class="keyboard-row">
        <button class="key">1</button>
        <button class="key">2</button>
        <button class="key">3</button>
        <button class="key key-confirm-old" id="oldBtn">完成</button>
      </div>
      <div class="keyboard-row">
        <button class="key">.</button>
        <button class="key">0</button>
        <button class="key">⌫</button>
        <button class="key key-confirm-old" id="oldBtnDisabled" disabled>完成</button>
      </div>
    </div>
    <div class="status-indicator status-disabled">
      ❌ 问题：完成按钮在某些情况下文字不清晰，禁用状态对比度不够
    </div>
    
    <h4>修复后（正确的版本）：</h4>
    <div class="keyboard-old">
      <div class="keyboard-row">
        <button class="key">7</button>
        <button class="key">8</button>
        <button class="key">9</button>
        <button class="key">+</button>
      </div>
      <div class="keyboard-row">
        <button class="key">4</button>
        <button class="key">5</button>
        <button class="key">6</button>
        <button class="key">-</button>
      </div>
      <div class="keyboard-row">
        <button class="key">1</button>
        <button class="key">2</button>
        <button class="key">3</button>
        <button class="key key-confirm-new" id="newBtn">完成</button>
      </div>
      <div class="keyboard-row">
        <button class="key">.</button>
        <button class="key">0</button>
        <button class="key">⌫</button>
        <button class="key key-confirm-new" id="newBtnDisabled" disabled>完成</button>
      </div>
    </div>
    <div class="status-indicator status-enabled">
      ✅ 修复：完成按钮文字清晰可见，禁用状态对比度良好
    </div>
  </div>

  <!-- 交互测试 -->
  <div class="test-section">
    <div class="test-title">交互测试</div>
    
    <p>测试按钮的启用/禁用状态切换：</p>
    
    <button class="toggle-btn" onclick="toggleButtonState()">切换按钮状态</button>
    <button class="toggle-btn" onclick="testButtonClick()">测试点击效果</button>
    
    <div id="currentStatus" class="status-indicator status-enabled">
      当前状态：启用
    </div>
    
    <div class="keyboard-old">
      <div class="keyboard-row">
        <button class="key">1</button>
        <button class="key">2</button>
        <button class="key">3</button>
        <button class="key key-confirm-new" id="testBtn" onclick="onTestClick()">完成</button>
      </div>
    </div>
    
    <div id="clickResult" style="margin-top: 10px; font-weight: bold;"></div>
  </div>

  <!-- 视觉对比测试 -->
  <div class="test-section">
    <div class="test-title">视觉对比测试</div>
    
    <table style="width: 100%; border-collapse: collapse;">
      <thead>
        <tr style="background: #f8f9fa;">
          <th style="padding: 12px; border: 1px solid #dee2e6;">状态</th>
          <th style="padding: 12px; border: 1px solid #dee2e6;">修复前</th>
          <th style="padding: 12px; border: 1px solid #dee2e6;">修复后</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">正常状态</td>
          <td style="padding: 12px; border: 1px solid #dee2e6;">
            <button class="key key-confirm-old" style="width: 80px; height: 40px;">完成</button>
          </td>
          <td style="padding: 12px; border: 1px solid #dee2e6;">
            <button class="key key-confirm-new" style="width: 80px; height: 40px;">完成</button>
          </td>
        </tr>
        <tr>
          <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">禁用状态</td>
          <td style="padding: 12px; border: 1px solid #dee2e6;">
            <button class="key key-confirm-old" style="width: 80px; height: 40px;" disabled>完成</button>
          </td>
          <td style="padding: 12px; border: 1px solid #dee2e6;">
            <button class="key key-confirm-new" style="width: 80px; height: 40px;" disabled>完成</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <script>
    let isEnabled = true;
    
    function toggleButtonState() {
      const testBtn = document.getElementById('testBtn');
      const statusDiv = document.getElementById('currentStatus');
      
      isEnabled = !isEnabled;
      testBtn.disabled = !isEnabled;
      
      if (isEnabled) {
        statusDiv.textContent = '当前状态：启用';
        statusDiv.className = 'status-indicator status-enabled';
      } else {
        statusDiv.textContent = '当前状态：禁用';
        statusDiv.className = 'status-indicator status-disabled';
      }
    }
    
    function testButtonClick() {
      const testBtn = document.getElementById('testBtn');
      testBtn.style.transform = 'translateY(-2px)';
      setTimeout(() => {
        testBtn.style.transform = 'translateY(0)';
      }, 150);
    }
    
    function onTestClick() {
      const resultDiv = document.getElementById('clickResult');
      if (isEnabled) {
        resultDiv.textContent = '✅ 按钮点击成功！';
        resultDiv.style.color = '#155724';
      } else {
        resultDiv.textContent = '❌ 按钮已禁用，无法点击';
        resultDiv.style.color = '#721c24';
      }
      
      setTimeout(() => {
        resultDiv.textContent = '';
      }, 2000);
    }
  </script>
</body>
</html>
