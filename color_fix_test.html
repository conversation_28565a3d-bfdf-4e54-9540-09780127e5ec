<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>完成按钮颜色修复测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    
    .test-section {
      background: white;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .keyboard-demo {
      width: 300px;
      margin: 20px auto;
      padding: 16px;
      background: #f7f7fa;
      border-radius: 18px;
      box-shadow: 0 -2px 16px rgba(0,0,0,0.06);
    }
    
    .keyboard-row {
      display: flex;
      width: 100%;
      margin-bottom: 8px;
    }
    
    /* 基础按键样式 */
    .key {
      flex: 1;
      margin: 0 6px;
      height: 48px;
      background: #fff;
      border: none;
      border-radius: 12px;
      font-size: 20px;
      color: #222;  /* 这里是深色文字 */
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      transition: background 0.15s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      cursor: pointer;
    }
    
    .key:active {
      background: #f0e9d2;
    }
    
    /* 修复前的完成按钮（有问题） */
    .key-confirm-old {
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      color: #ffffff;  /* 白色文字，但可能被基础样式覆盖 */
      font-weight: bold;
      font-size: 18px;
    }
    
    /* 修复后的完成按钮（正确版本） */
    button.key.key-confirm-new {
      background: #667eea !important;
      color: #ffffff !important;  /* 强制白色文字 */
      font-weight: bold !important;
      font-size: 18px !important;
      border-radius: 12px !important;
      text-shadow: none !important;
      border: none !important;
      box-shadow: 0 2px 8px rgba(102,126,234,0.3) !important;
    }
    
    button.key.key-confirm-new:hover {
      background: #5a6fd8 !important;
      color: #ffffff !important;
    }
    
    button.key.key-confirm-new:disabled {
      background: #e2e8f0 !important;
      color: #a0aec0 !important;
      opacity: 1 !important;
      cursor: not-allowed !important;
    }
    
    .comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    
    .comparison-table th,
    .comparison-table td {
      padding: 12px;
      border: 1px solid #ddd;
      text-align: center;
    }
    
    .comparison-table th {
      background: #f8f9fa;
      font-weight: bold;
    }
    
    .problem {
      background: #f8d7da;
      color: #721c24;
    }
    
    .fixed {
      background: #d4edda;
      color: #155724;
    }
  </style>
</head>
<body>
  <h1>完成按钮颜色修复测试</h1>
  
  <div class="test-section">
    <h3>问题分析</h3>
    <p><strong>根本原因</strong>：基础的 <code>.key</code> 样式设置了 <code>color: #222</code>（深色），完成按钮继承了这个样式，导致白色文字被覆盖。</p>
    
    <table class="comparison-table">
      <thead>
        <tr>
          <th>样式优先级</th>
          <th>修复前</th>
          <th>修复后</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><strong>CSS选择器</strong></td>
          <td class="problem">.key-confirm { color: #ffffff; }</td>
          <td class="fixed">button.key.key-confirm { color: #ffffff !important; }</td>
        </tr>
        <tr>
          <td><strong>优先级权重</strong></td>
          <td class="problem">0,0,1,0 (较低)</td>
          <td class="fixed">0,0,2,1 + !important (最高)</td>
        </tr>
        <tr>
          <td><strong>实际效果</strong></td>
          <td class="problem">被 .key 样式覆盖</td>
          <td class="fixed">成功覆盖基础样式</td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div class="test-section">
    <h3>修复前后对比</h3>
    
    <h4>修复前（有问题的版本）：</h4>
    <div class="keyboard-demo">
      <div class="keyboard-row">
        <button class="key">1</button>
        <button class="key">2</button>
        <button class="key">3</button>
      </div>
      <div class="keyboard-row">
        <button class="key">.</button>
        <button class="key">0</button>
        <button class="key">⌫</button>
      </div>
      <div class="keyboard-row">
        <button class="key key-confirm-old" style="flex: 3;">完成</button>
      </div>
    </div>
    <p class="problem">❌ 问题：完成按钮的文字颜色被基础样式覆盖，在深色背景上看不清</p>
    
    <h4>修复后（正确的版本）：</h4>
    <div class="keyboard-demo">
      <div class="keyboard-row">
        <button class="key">1</button>
        <button class="key">2</button>
        <button class="key">3</button>
      </div>
      <div class="keyboard-row">
        <button class="key">.</button>
        <button class="key">0</button>
        <button class="key">⌫</button>
      </div>
      <div class="keyboard-row">
        <button class="key key-confirm-new" style="flex: 3;">完成</button>
      </div>
    </div>
    <p class="fixed">✅ 修复：使用更高优先级的选择器和 !important，确保白色文字显示</p>
  </div>
  
  <div class="test-section">
    <h3>状态测试</h3>
    
    <div style="display: flex; gap: 20px; justify-content: center; margin: 20px 0;">
      <div style="text-align: center;">
        <p><strong>正常状态</strong></p>
        <button class="key key-confirm-new" style="width: 100px; height: 48px;">完成</button>
      </div>
      <div style="text-align: center;">
        <p><strong>悬停状态</strong></p>
        <button class="key key-confirm-new" style="width: 100px; height: 48px; background: #5a6fd8 !important;">完成</button>
      </div>
      <div style="text-align: center;">
        <p><strong>禁用状态</strong></p>
        <button class="key key-confirm-new" style="width: 100px; height: 48px;" disabled>完成</button>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h3>修复要点总结</h3>
    <ul>
      <li>✅ <strong>提高选择器优先级</strong>：使用 <code>button.key.key-confirm</code> 替代 <code>.key-confirm</code></li>
      <li>✅ <strong>使用 !important</strong>：确保关键样式不被覆盖</li>
      <li>✅ <strong>内联样式辅助</strong>：在HTML中添加 <code>style="color: #ffffff !important;"</code></li>
      <li>✅ <strong>所有状态覆盖</strong>：包括 :hover、:active、:disabled 状态</li>
      <li>✅ <strong>移除渐变背景</strong>：使用纯色背景避免复杂度</li>
    </ul>
  </div>
  
  <div class="test-section">
    <h3>CSS优先级说明</h3>
    <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
/* 优先级从低到高 */
.key                           /* 0,0,1,0 */
.key-confirm                   /* 0,0,1,0 */
button.key                     /* 0,0,1,1 */
button.key.key-confirm         /* 0,0,2,1 */
button.key.key-confirm !important  /* 最高优先级 */
    </pre>
  </div>
</body>
</html>
