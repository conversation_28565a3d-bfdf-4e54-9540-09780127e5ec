<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>完成按钮修复测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .phone-mockup {
      width: 375px;
      height: 600px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 24px;
      overflow: hidden;
      margin: 0 auto;
      position: relative;
      box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    }
    
    .amount-display {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      font-weight: bold;
      color: #2d3748;
      background: rgba(255,255,255,0.9);
      margin: 20px;
      border-radius: 16px;
    }
    
    .custom-keyboard {
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 16px;
      background: #f7f7fa;
      border-radius: 18px 18px 0 0;
      box-shadow: 0 -2px 16px rgba(0,0,0,0.06);
    }
    
    .keyboard-row {
      display: flex;
      width: 100%;
      margin-bottom: 8px;
    }
    
    .key {
      flex: 1;
      margin: 0 6px;
      height: 48px;
      background: #fff;
      border: none;
      border-radius: 12px;
      font-size: 20px;
      color: #222;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      transition: background 0.15s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      cursor: pointer;
    }
    
    .key:active {
      background: #f0e9d2;
    }
    
    /* 修复后的完成按钮样式 */
    .key-confirm {
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
      color: #ffffff !important;
      font-weight: bold !important;
      font-size: 18px !important;
      border-radius: 12px !important;
      text-shadow: none !important;
      border: none !important;
      box-shadow: 0 2px 8px rgba(102,126,234,0.3) !important;
      position: relative !important;
      overflow: visible !important;
      height: 48px !important;
    }
    
    .key-confirm span {
      color: #ffffff !important;
      font-weight: bold !important;
      font-size: 18px !important;
      text-shadow: none !important;
      display: block !important;
    }
    
    .key-confirm:hover {
      background: linear-gradient(90deg, #5a6fd8 0%, #6a4190 100%) !important;
      transform: translateY(-1px);
    }
    
    .key-confirm:active {
      background: linear-gradient(90deg, #4e5bc7 0%, #5e377f 100%) !important;
      transform: translateY(0);
    }
    
    .key-confirm:disabled {
      background: #cbd5e0 !important;
      color: #718096 !important;
      opacity: 1 !important;
      cursor: not-allowed !important;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
      font-weight: normal !important;
    }
    
    .key-confirm:disabled span {
      color: #718096 !important;
      font-weight: normal !important;
    }
    
    .key-confirm:disabled:hover {
      transform: none !important;
    }
    
    .test-controls {
      text-align: center;
      margin: 20px 0;
    }
    
    .test-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      margin: 0 10px;
      cursor: pointer;
    }
    
    .test-btn:hover {
      background: #0056b3;
    }
    
    .status {
      text-align: center;
      margin: 10px 0;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1 style="text-align: center;">完成按钮修复测试</h1>
  
  <div class="test-controls">
    <button class="test-btn" onclick="toggleButtonState()">切换按钮状态</button>
    <button class="test-btn" onclick="clearAmount()">清空金额</button>
    <button class="test-btn" onclick="setAmount()">设置金额</button>
  </div>
  
  <div class="status" id="status">当前状态：按钮启用</div>
  
  <div class="phone-mockup">
    <div class="amount-display" id="amountDisplay">0.00</div>
    
    <div class="custom-keyboard">
      <div class="keyboard-row">
        <button class="key" onclick="inputNumber('7')">7</button>
        <button class="key" onclick="inputNumber('8')">8</button>
        <button class="key" onclick="inputNumber('9')">9</button>
        <button class="key" onclick="inputNumber('+')">+</button>
      </div>
      <div class="keyboard-row">
        <button class="key" onclick="inputNumber('4')">4</button>
        <button class="key" onclick="inputNumber('5')">5</button>
        <button class="key" onclick="inputNumber('6')">6</button>
        <button class="key" onclick="inputNumber('-')">-</button>
      </div>
      <div class="keyboard-row">
        <button class="key" onclick="inputNumber('1')">1</button>
        <button class="key" onclick="inputNumber('2')">2</button>
        <button class="key" onclick="inputNumber('3')">3</button>
      </div>
      <div class="keyboard-row">
        <button class="key" onclick="inputNumber('.')">.</button>
        <button class="key" onclick="inputNumber('0')">0</button>
        <button class="key" onclick="deleteNumber()">⌫</button>
      </div>
      <div class="keyboard-row">
        <button class="key key-confirm" id="confirmBtn" onclick="confirmAmount()">
          <span>完成</span>
        </button>
      </div>
    </div>
  </div>
  
  <div style="text-align: center; margin-top: 20px;">
    <h3>修复要点：</h3>
    <ul style="text-align: left; display: inline-block;">
      <li>✅ 完成按钮独占一行，避免被挤压</li>
      <li>✅ 使用渐变背景，提高视觉吸引力</li>
      <li>✅ 白色文字确保在深色背景上清晰可见</li>
      <li>✅ 禁用状态使用灰色背景和深灰色文字</li>
      <li>✅ 添加阴影和悬停效果增强交互感</li>
      <li>✅ 使用 !important 确保样式优先级</li>
    </ul>
  </div>

  <script>
    let currentAmount = '';
    let isButtonEnabled = true;
    
    function inputNumber(num) {
      if (num === '+' || num === '-') {
        if (currentAmount && !/[+\-]$/.test(currentAmount)) {
          currentAmount += num;
        }
      } else if (num === '.') {
        const parts = currentAmount.split(/[+\-]/);
        const last = parts[parts.length - 1];
        if (last && !last.includes('.')) {
          currentAmount += num;
        }
      } else {
        currentAmount += num;
      }
      updateDisplay();
    }
    
    function deleteNumber() {
      currentAmount = currentAmount.slice(0, -1);
      updateDisplay();
    }
    
    function updateDisplay() {
      const display = document.getElementById('amountDisplay');
      const confirmBtn = document.getElementById('confirmBtn');
      
      if (currentAmount) {
        try {
          const result = eval(currentAmount.replace(/[^\d.+\-]/g, ''));
          display.textContent = result ? result.toFixed(2) : currentAmount || '0.00';
        } catch {
          display.textContent = currentAmount || '0.00';
        }
      } else {
        display.textContent = '0.00';
      }
      
      // 更新按钮状态
      const hasAmount = currentAmount && currentAmount !== '';
      confirmBtn.disabled = !hasAmount || !isButtonEnabled;
      updateStatus();
    }
    
    function confirmAmount() {
      if (currentAmount) {
        alert(`记账成功！金额：${document.getElementById('amountDisplay').textContent}`);
        currentAmount = '';
        updateDisplay();
      }
    }
    
    function toggleButtonState() {
      isButtonEnabled = !isButtonEnabled;
      updateDisplay();
    }
    
    function clearAmount() {
      currentAmount = '';
      updateDisplay();
    }
    
    function setAmount() {
      currentAmount = '100.50';
      updateDisplay();
    }
    
    function updateStatus() {
      const status = document.getElementById('status');
      const confirmBtn = document.getElementById('confirmBtn');
      
      if (confirmBtn.disabled) {
        if (!isButtonEnabled) {
          status.textContent = '当前状态：按钮被禁用（手动禁用）';
          status.style.color = '#e53e3e';
        } else {
          status.textContent = '当前状态：按钮被禁用（无金额）';
          status.style.color = '#f56500';
        }
      } else {
        status.textContent = '当前状态：按钮启用';
        status.style.color = '#38a169';
      }
    }
    
    // 初始化
    updateDisplay();
  </script>
</body>
</html>
