/* 全局主题样式 */

/* 现代科技主题 */
.theme-modern .page-background {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.theme-modern .primary-gradient {
  background: linear-gradient(120deg, #667eea 0%, #764ba2 100%);
}

.theme-modern .primary-color {
  color: #667eea;
}

.theme-modern .primary-bg {
  background-color: #667eea;
}

/* 温暖橙光主题 */
.theme-warm .page-background {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.theme-warm .primary-gradient {
  background: linear-gradient(120deg, #ff9a9e 0%, #fecfef 100%);
}

.theme-warm .primary-color {
  color: #ff9a9e;
}

.theme-warm .primary-bg {
  background-color: #ff9a9e;
}

/* 自然绿意主题 */
.theme-nature .page-background {
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
}

.theme-nature .primary-gradient {
  background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
}

.theme-nature .primary-color {
  color: #a8edea;
}

.theme-nature .primary-bg {
  background-color: #a8edea;
}

/* 优雅紫调主题 */
.theme-elegant .page-background {
  background: linear-gradient(135deg, #f9f0ff 0%, #ede7f6 100%);
}

.theme-elegant .primary-gradient {
  background: linear-gradient(120deg, #d299c2 0%, #fef9d7 100%);
}

.theme-elegant .primary-color {
  color: #d299c2;
}

.theme-elegant .primary-bg {
  background-color: #d299c2;
}

/* 海洋蓝调主题 */
.theme-ocean .page-background {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.theme-ocean .primary-gradient {
  background: linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%);
}

.theme-ocean .primary-color {
  color: #66a6ff;
}

.theme-ocean .primary-bg {
  background-color: #66a6ff;
}

/* 通用主题样式 */
.themed-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.themed-button {
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.themed-button.primary {
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.themed-button.primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 主题过渡动画 */
.theme-transition {
  transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
