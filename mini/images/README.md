<!--
 * @Author: 黄忠平 <EMAIL>
 * @Date: 2025-07-04 16:08:36
 * @LastEditors: 黄忠平 <EMAIL>
 * @LastEditTime: 2025-07-04 18:09:17
 * @FilePath: /ai/mini/images/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 图片资源说明

## 需要添加的图片文件

请将以下图片文件放置在此目录下：

### 1. wechat-qr.png
- **用途**: 微信公众号二维码
- **尺寸**: 建议 300x300 像素
- **格式**: PNG（支持透明背景）
- **说明**: 用户扫描此二维码可关注您的微信公众号

### 2. share-cover.png (可选)
- **用途**: 分享封面图
- **尺寸**: 建议 500x400 像素  
- **格式**: PNG 或 JPG
- **说明**: 用户分享小程序时显示的封面图

## 图片要求

- 清晰度高，适合移动端显示
- 文件大小控制在 200KB 以内
- 二维码图片建议有一定的容错率
- 可以添加简单的装饰边框或背景

## 使用说明

1. 将您的微信公众号二维码保存为 `wechat-qr.png`
2. 放置在 `/images/` 目录下
3. 重新编译小程序即可看到效果

如果暂时没有二维码图片，页面会显示占位图片，不影响其他功能的使用。
