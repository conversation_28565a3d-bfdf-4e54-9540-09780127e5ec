/* 记账页面骨架屏样式 */
.skeleton-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 120px;
}

.skeleton-tab-container {
  display: flex;
  justify-content: center;
  margin: 20px auto 0 auto;
  width: 100%;
}

.skeleton-tab {
  display: flex;
  justify-content: center;
  background: white;
  border-radius: 18px;
  padding: 4px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  margin: 0 18px;
  gap: 6px;
}

.skeleton-tab-item {
  width: 80px;
  height: 32px;
  background: #e2e8f0;
  border-radius: 14px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-category-section {
  margin-top: 32px;
  padding: 0 18px;
}

.skeleton-category-grid {
  background: white;
  border-radius: 20px;
  padding: 24px 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.skeleton-category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.skeleton-category-icon {
  width: 48px;
  height: 48px;
  background: #e2e8f0;
  border-radius: 50%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-category-label {
  width: 40px;
  height: 14px;
  background: #e2e8f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
