Page({
  data: {
    activeType: 0, // 0-支出 1-收入
    tabList: ['支出', '收入'],
    currentTheme: 'modern', // 当前主题
    // 分类icon和名称，参考记账页_vant.html
    categoriesMap: {
      '支出': [
        { name: '餐饮', icon: 'bill-o', emoji: '🍽️' },
        { name: '购物', icon: 'cart-o', emoji: '🛒' },
        { name: '交通', icon: 'guide-o', emoji: '🚗' },
        { name: '娱乐', icon: 'smile-o', emoji: '🎮' },
        { name: '医疗', icon: 'medal-o', emoji: '🏥' },
        { name: '教育', icon: 'edit', emoji: '📚' },
        { name: '住房', icon: 'home-o', emoji: '🏠' },
        { name: '通讯', icon: 'phone-o', emoji: '📱' },
        { name: '服装', icon: 'bag-o', emoji: '👕' },
        { name: '美容', icon: 'beauty', emoji: '💄' },
        { name: '运动', icon: 'play-circle-o', emoji: '⚽' },
        { name: '旅行', icon: 'location-o', emoji: '✈️' },
        { name: '礼物', icon: 'gift-o', emoji: '🎁' },
        { name: '宠物', icon: 'like-o', emoji: '🐕' },
        { name: '其他', icon: 'ellipsis', emoji: '📝' }
      ],
      '收入': [
        { name: '工资', icon: 'gold-coin-o', emoji: '💰' },
        { name: '理财', icon: 'balance-o', emoji: '📈' },
        { name: '红包', icon: 'red-packet', emoji: '🧧' },
        { name: '兼职', icon: 'cash-back-record-o', emoji: '💼' },
        { name: '奖金', icon: 'award-o', emoji: '🏆' },
        { name: '投资', icon: 'chart-trending-o', emoji: '📊' },
        { name: '退款', icon: 'refund-o', emoji: '💳' },
        { name: '其他', icon: 'ellipsis', emoji: '💵' }
      ]
    },
    categories: [],
    selectedCatIdx: -1,
    selectedCategory: null,
    amountExpr: '',
    amountDisplay: '0.00',
    remark: '',
    date: '',
    dateValue: '',
    timeValue: '',
    showDatePicker: false,
    showKeyboard: false,
    showRemarkPopup: false,
    showRecordDialog: false,
    dateLabel: '今天',
    timeLabel: '',
    canSave: false
  },
  onLoad() {
    this.loadTheme();
    this.setTab(0);
    const today = this.getTodayObj();
    const currentTime = this.getCurrentTime();
    this.setData({
      dateValue: today,
      timeValue: currentTime,
      date: this.formatDateTime(today, currentTime),
      dateLabel: '今天',
      timeLabel: this.formatTimeLabel(currentTime)
    });
  },

  onShow() {
    this.loadTheme();
  },
  setTab(typeIdx) {
    const type = this.data.tabList[typeIdx];
    const categories = this.data.categoriesMap[type];
    this.setData({
      activeType: typeIdx,
      categories,
      selectedCatIdx: -1,
      selectedCategory: null
    });
    this.updateCanSave();
  },
  onTabChange(e) {
    const idx = Number(e.currentTarget.dataset.type);
    this.setTab(idx);
  },
  updateCanSave() {
    let hasValidAmount = false;
    let calculatedAmount = 0;
    if (this.data.amountExpr) {
      try {
        // 使用安全的计算方法，不使用eval
        calculatedAmount = this.calculateAmountSafe(this.data.amountExpr);
        hasValidAmount = calculatedAmount > 0;
      } catch (error) {
        console.log('金额计算错误:', error);
        hasValidAmount = false;
      }
    }
    const hasCategory = this.data.selectedCategory !== null && this.data.selectedCategory !== undefined;
    const canSave = hasValidAmount && hasCategory;



    this.setData({
      canSave
    });
  },

  // 安全的金额计算方法，不使用eval
  calculateAmountSafe(expr) {
    if (!expr) return 0;

    // 简单的表达式解析，支持加减法
    try {
      // 清理表达式，只保留数字、小数点、加号、减号
      const cleanExpr = expr.replace(/[^\d.+\-]/g, '');
      if (!cleanExpr) return 0;

      // 如果只是数字，直接返回
      if (/^\d+\.?\d*$/.test(cleanExpr)) {
        return Number(cleanExpr);
      }

      // 处理加减运算
      let result = 0;
      let currentNumber = '';
      let operator = '+';

      for (let i = 0; i < cleanExpr.length; i++) {
        const char = cleanExpr[i];

        if (char === '+' || char === '-') {
          if (currentNumber) {
            if (operator === '+') {
              result += Number(currentNumber);
            } else {
              result -= Number(currentNumber);
            }
            currentNumber = '';
          }
          operator = char;
        } else {
          currentNumber += char;
        }
      }

      // 处理最后一个数字
      if (currentNumber) {
        if (operator === '+') {
          result += Number(currentNumber);
        } else {
          result -= Number(currentNumber);
        }
      }

      return result;
    } catch {
      return 0;
    }
  },

  calculateAmount() {
    return this.calculateAmountSafe(this.data.amountExpr);
  },
  onCatSelect(e) {
    const idx = Number(e.currentTarget.dataset.idx);
    const category = this.data.categories[idx];

    this.setData({
      selectedCatIdx: idx,
      selectedCategory: category,
      showRecordDialog: true
    }, () => {
      // 在setData回调中调用updateCanSave，确保数据已更新
      this.updateCanSave();
    });
  },
  // 记账对话框相关方法
  onRecordDialogClose() {
    this.setData({
      showRecordDialog: false,
      selectedCatIdx: -1,
      selectedCategory: null,
      amountExpr: '',
      amountDisplay: '0.00',
      remark: '',
      canSave: false
    });
  },

  onDialogAmountTap() {
    // 在记账对话框中，金额输入直接通过键盘操作，不需要额外弹窗
  },

  onDialogSave() {
    // 检查是否可以保存
    if (!this.data.canSave) {
      return;
    }

    // 先计算最终金额
    let finalAmount = 0;
    try {
      if (this.data.amountExpr) {
        const result = this.calculateAmountSafe(this.data.amountExpr);
        finalAmount = result ? Number(result.toFixed(2)) : 0;
      }
    } catch {
      finalAmount = 0;
    }

    // 验证数据
    if (!finalAmount || finalAmount <= 0) {
      wx.showToast({ title: '请输入有效金额', icon: 'none' });
      return;
    }

    if (!this.data.selectedCategory) {
      wx.showToast({ title: '请选择分类', icon: 'none' });
      return;
    }

    // 创建记账记录
    const type = this.data.activeType === 0 ? '支出' : '收入';
    const cat = this.data.selectedCategory;
    const bill = {
      id: Date.now(),
      type,
      category: cat.name,
      icon: cat.icon,
      emoji: cat.emoji,
      amount: finalAmount,
      date: this.data.date,
      remark: this.data.remark || '',
      timestamp: new Date().getTime()
    };

    // 保存到本地存储
    let bills = wx.getStorageSync('bills') || [];
    bills.unshift(bill); // 新账单放最前
    wx.setStorageSync('bills', bills);

    // 显示成功提示
    wx.showToast({
      title: '记账成功',
      icon: 'success',
      duration: 1500
    });

    // 重置表单并关闭对话框
    this.setData({
      amountExpr: '',
      amountDisplay: '0.00',
      remark: '',
      selectedCatIdx: -1,
      selectedCategory: null,
      showRecordDialog: false,
      canSave: false
    });

    // 延迟跳转到首页
    setTimeout(() => {
      wx.switchTab({ url: '/pages/home/<USER>' });
    }, 1500);
  },

  onAmountTap() {
    this.setData({ showKeyboard: true });
  },
  onKeyboardClose() {
    this.setData({ showKeyboard: false });
  },
  onRemarkTap() {
    this.setData({ showRemarkPopup: true });
  },
  onRemarkClose() {
    this.setData({ showRemarkPopup: false });
  },
  onRemarkChange(e) {
    this.setData({ remark: e.detail });
  },
  onRemarkConfirm() {
    this.setData({ showRemarkPopup: false });
  },
  onDateTap() {
    this.setData({ showDatePicker: true });
  },
  onKey(e) {
    const key = e.currentTarget.dataset.key;
    let expr = this.data.amountExpr;

    if (key === 'delete') {
      expr = expr.slice(0, -1);
    } else if (key === 'today') {
      // 打开日期时间选择器
      this.setData({ showDatePicker: true });
      return;
    } else if (key === '+' || key === '-') {
      if (expr && !/[+\-]$/.test(expr)) expr += key;
    } else if (key === '.') {
      const parts = expr.split(/[+\-]/);
      const last = parts[parts.length - 1];
      if (last && !last.includes('.')) expr += key;
    } else if (/\d/.test(key)) {
      expr += key;
    }

    // 更新显示金额 - 显示表达式，参考HTML文件
    const display = expr || '0.00';

    this.setData({
      amountExpr: expr,
      amountDisplay: display
    }, () => {
      // 在setData回调中调用updateCanSave，确保数据已更新
      this.updateCanSave();
    });
  },

  calculateAmountFromExpr(expr) {
    return this.calculateAmountSafe(expr);
  },

  onKeyboardConfirm() {
    const amount = this.calculateAmountFromExpr(this.data.amountExpr);
    if (amount > 0) {
      this.setData({
        showKeyboard: false,
        amountDisplay: amount.toFixed(2)
      });
    } else {
      wx.showToast({ title: '请输入有效金额', icon: 'none' });
    }
  },
  onDateClose() {
    this.setData({ showDatePicker: false });
  },

  // 日期选择变化
  onDateChange(e) {
    const dateValue = e.detail.value;
    this.setData({ dateValue });
    this.updateDateTimeDisplay();
  },

  // 时间选择变化
  onTimeChange(e) {
    const timeValue = e.detail.value;
    this.setData({ timeValue });
    this.updateDateTimeDisplay();
  },

  // 确认日期时间选择
  onDateTimeConfirm() {
    this.setData({ showDatePicker: false });
  },

  // 更新日期时间显示
  updateDateTimeDisplay() {
    const { dateValue, timeValue } = this.data;
    const today = this.getTodayObj();
    const dateTime = this.formatDateTime(dateValue, timeValue);
    const isToday = dateValue === today;

    this.setData({
      date: dateTime,
      dateLabel: isToday ? '今天' : this.formatDate(dateValue),
      timeLabel: this.formatTimeLabel(timeValue)
    });
  },
  getTodayObj() {
    const now = new Date();
    return `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')}`;
  },

  getCurrentTime() {
    const now = new Date();
    return `${now.getHours().toString().padStart(2,'0')}:${now.getMinutes().toString().padStart(2,'0')}`;
  },

  formatDate(dateStr) {
    // dateStr: yyyy-mm-dd
    if (!dateStr) return '';
    const arr = dateStr.split('-');
    return `${arr[1]}-${arr[2]}`;
  },

  formatDateTime(dateStr, timeStr) {
    // 组合日期和时间，用于存储
    if (!dateStr || !timeStr) return '';
    return `${dateStr} ${timeStr}`;
  },

  formatTimeLabel(timeStr) {
    // 格式化时间显示标签
    if (!timeStr) return '';
    return timeStr;
  },
  onSave() {
    if (!this.data.canSave) {
      wx.showToast({ title: '请完善记账信息', icon: 'none' });
      return;
    }

    const amount = this.calculateAmountFromExpr(this.data.amountExpr);
    if (!amount || amount <= 0) {
      wx.showToast({ title: '请输入有效金额', icon: 'none' });
      return;
    }

    const type = this.data.activeType === 0 ? '支出' : '收入';
    const cat = this.data.categories[this.data.selectedCatIdx];
    const bill = {
      id: Date.now(),
      type,
      category: cat.name,
      icon: cat.icon,
      amount: Number(amount.toFixed(2)),
      date: this.data.date,
      remark: this.data.remark || ''
    };

    let bills = wx.getStorageSync('bills') || [];
    bills.unshift(bill); // 新账单放最前
    wx.setStorageSync('bills', bills);

    wx.showToast({ title: '记账成功', icon: 'success' });

    // 重置表单
    this.setData({
      amountExpr: '',
      amountDisplay: '0.00',
      remark: '',
      selectedCatIdx: 0,
      canSave: false
    });

    setTimeout(() => {
      wx.switchTab({ url: '/pages/home/<USER>' });
    }, 1000);
  },

  /**
   * 加载主题
   */
  loadTheme() {
    const app = getApp();
    const currentTheme = app.globalData.currentTheme;
    this.setData({ currentTheme });
  },

  /**
   * 主题变更回调
   */
  onThemeChange(themeId) {
    this.setData({ currentTheme: themeId });
  }
});
