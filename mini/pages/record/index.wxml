<!-- 记账页新版结构，参考UI.html和vant组件 -->
<view class="record-root theme-{{currentTheme}}">
  <!-- 支出/收入tab -->
  <view class="record-tab-container">
    <view class="record-tab">
      <view class="tab-item {{activeType===0?'tab-active':'tab-inactive'}}" data-type="0" bindtap="onTabChange">支出</view>
      <view class="tab-item {{activeType===1?'tab-active':'tab-inactive'}}" data-type="1" bindtap="onTabChange">收入</view>
    </view>
  </view>

  <!-- 分类宫格 -->
  <view class="category-section">
    <view class="category-grid">
      <view class="cat-btn {{selectedCatIdx===index?'selected':''}}" wx:for="{{categories}}" wx:key="name" data-idx="{{index}}" bindtap="onCatSelect">
        <view class="cat-icon-wrap">
          <text class="cat-icon-emoji">{{item.emoji}}</text>
        </view>
        <view class="cat-label">{{item.name}}</view>
      </view>
    </view>
  </view>

</view>

<!-- 记账对话框 -->
<van-popup
  show="{{showRecordDialog}}"
  position="bottom"
  round
  custom-class="record-dialog-popup"
  bind:close="onRecordDialogClose"
>
  <view class="record-dialog">
    <!-- 分类信息显示 -->
    <view class="dialog-category-header">
      <view class="dialog-cat-icon" wx:if="{{selectedCategory}}">
        <text class="cat-icon-emoji">{{selectedCategory.emoji}}</text>
      </view>
      <view class="dialog-cat-name" wx:if="{{selectedCategory}}">{{selectedCategory.name}}</view>
    </view>

    <!-- 金额显示 -->
    <view class="dialog-amount-section">
      <view class="dialog-amount-value" bindtap="onDialogAmountTap">{{amountDisplay}}</view>

    </view>

    <!-- 备注输入 -->
    <view class="dialog-remark-section">
      <van-field
        value="{{remark}}"
        placeholder="备注"
        bind:change="onRemarkChange"
        custom-style="background: #f7f7fa; border-radius: 8px; flex: 1;"
      />
    </view>

    <!-- 自定义键盘 -->
    <view class="dialog-custom-keyboard">
      <view class="keyboard-row">
        <view class="key" data-key="7" bindtap="onKey">7</view>
        <view class="key" data-key="8" bindtap="onKey">8</view>
        <view class="key" data-key="9" bindtap="onKey">9</view>
        <view class="key key-today" data-key="today" bindtap="onKey" catchtap="onKey">
          <van-icon name="calendar-o" size="16" />
          <text>今天</text>
        </view>
      </view>
      <view class="keyboard-row">
        <view class="key" data-key="4" bindtap="onKey">4</view>
        <view class="key" data-key="5" bindtap="onKey">5</view>
        <view class="key" data-key="6" bindtap="onKey">6</view>
        <view class="key" data-key="+" bindtap="onKey">+</view>
      </view>
      <view class="keyboard-row">
        <view class="key" data-key="1" bindtap="onKey">1</view>
        <view class="key" data-key="2" bindtap="onKey">2</view>
        <view class="key" data-key="3" bindtap="onKey">3</view>
        <view class="key" data-key="-" bindtap="onKey">-</view>
      </view>
      <view class="keyboard-row">
        <view class="key" data-key="." bindtap="onKey">.</view>
        <view class="key" data-key="0" bindtap="onKey">0</view>
        <view class="key" data-key="delete" bindtap="onKey">
          <van-icon name="delete" />
        </view>
        <view class="key key-confirm {{canSave ? '' : 'key-disabled'}}" bindtap="onDialogSave">
          完成{{canSave ? '✓' : '✗'}}
        </view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 金额输入键盘弹窗 -->
<van-popup
  show="{{showKeyboard}}"
  position="bottom"
  round
  custom-class="custom-keyboard-popup"
  bind:close="onKeyboardClose"
>
  <view class="keyboard-popup-content">
    <view class="keyboard-header">
      <view class="keyboard-title">输入金额</view>
      <view class="keyboard-amount">{{amountDisplay}}</view>
    </view>
    <view class="custom-keyboard">
      <view class="keyboard-row">
        <view class="key" data-key="7" bindtap="onKey">7</view>
        <view class="key" data-key="8" bindtap="onKey">8</view>
        <view class="key" data-key="9" bindtap="onKey">9</view>
        <view class="key" data-key="today" bindtap="onKey">
          <van-icon name="calendar-o" size="16" />
          <text>今天</text>
        </view>
      </view>
      <view class="keyboard-row">
        <view class="key" data-key="4" bindtap="onKey">4</view>
        <view class="key" data-key="5" bindtap="onKey">5</view>
        <view class="key" data-key="6" bindtap="onKey">6</view>
        <view class="key" data-key="+" bindtap="onKey">+</view>
      </view>
      <view class="keyboard-row">
        <view class="key" data-key="1" bindtap="onKey">1</view>
        <view class="key" data-key="2" bindtap="onKey">2</view>
        <view class="key" data-key="3" bindtap="onKey">3</view>
        <view class="key" data-key="-" bindtap="onKey">-</view>
      </view>
      <view class="keyboard-row">
        <view class="key" data-key="." bindtap="onKey">.</view>
        <view class="key" data-key="0" bindtap="onKey">0</view>
        <view class="key" data-key="delete" bindtap="onKey">
          <van-icon name="delete" />
        </view>
        <view class="key key-confirm" bindtap="onKeyboardConfirm">完成</view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 备注输入弹窗 -->
<van-popup
  show="{{showRemarkPopup}}"
  position="bottom"
  round
  bind:close="onRemarkClose"
>
  <view class="remark-popup">
    <view class="popup-header">
      <view class="popup-title">添加备注</view>
    </view>
    <van-field
      value="{{remark}}"
      placeholder="请输入备注信息"
      type="textarea"
      autosize
      maxlength="100"
      show-word-limit
      bind:change="onRemarkChange"
    />
    <view class="popup-actions">
      <van-button size="large" bind:click="onRemarkClose">取消</van-button>
      <van-button size="large" type="primary" bind:click="onRemarkConfirm">确定</van-button>
    </view>
  </view>
</van-popup>

<!-- 日期时间选择器弹窗 -->
<van-popup
  show="{{showDatePicker}}"
  position="bottom"
  round
  bind:close="onDateClose"
  z-index="9999"
>
  <view class="datetime-picker-popup">
    <view class="popup-header">
      <view class="popup-title">选择日期时间</view>
    </view>

    <!-- 测试内容 -->
    <view style="padding: 20px; text-align: center;">
      <text>日期时间选择器测试</text>
      <text>当前日期: {{dateValue}}</text>
      <text>当前时间: {{timeValue}}</text>
    </view>

    <!-- 日期选择 -->
    <view class="picker-section">
      <view class="picker-label">日期</view>
      <picker
        mode="date"
        value="{{dateValue}}"
        bind:change="onDateChange"
        class="date-picker"
      >
        <view class="picker-display">{{dateLabel}}</view>
      </picker>
    </view>

    <!-- 时间选择 -->
    <view class="picker-section">
      <view class="picker-label">时间</view>
      <picker
        mode="time"
        value="{{timeValue}}"
        bind:change="onTimeChange"
        class="time-picker"
      >
        <view class="picker-display">{{timeLabel}}</view>
      </picker>
    </view>

    <view class="popup-actions">
      <van-button size="large" bind:click="onDateClose">取消</van-button>
      <van-button size="large" type="primary" bind:click="onDateTimeConfirm">确定</van-button>
    </view>
  </view>
</van-popup>

<!-- 使用原生tabBar，无需在页面中定义 -->
