page {
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* 主题背景 */
.theme-modern page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.theme-warm page {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.theme-nature page {
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
}

.theme-elegant page {
  background: linear-gradient(135deg, #f9f0ff 0%, #ede7f6 100%);
}

.theme-ocean page {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

::-webkit-scrollbar { display: none; }

.record-root {
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* Tab样式 */
.record-tab-container {
  display: flex;
  justify-content: center;
  margin: 20px auto 0 auto;
  width: 100%;
}

.record-tab {
  display: flex;
  justify-content: center;
  background: #fff;
  border-radius: 18px;
  padding: 4px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  margin: 0 18px;
}

.tab-item {
  border-radius: 14px;
  margin: 0 6px;
  padding: 8px 24px;
  min-width: 80px;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.record-root .tab-item.tab-active {
  background: #667eea !important; /* 默认背景色，会被主题样式覆盖 */
  color: #fff !important;
  font-weight: bold !important;
  transition: all 0.3s ease !important;
  border-radius: 20px !important;
}

/* 主题标签激活样式 - 增加特异性 */
.record-root.theme-modern .tab-item.tab-active {
  background: #667eea !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(102,126,234,0.3) !important;
}

.record-root.theme-warm .tab-item.tab-active {
  background: #ff9a9e !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(255,154,158,0.3) !important;
}

.record-root.theme-nature .tab-item.tab-active {
  background: #a8edea !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(168,237,234,0.3) !important;
}

.record-root.theme-elegant .tab-item.tab-active {
  background: #d299c2 !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(210,153,194,0.3) !important;
}

.record-root.theme-ocean .tab-item.tab-active {
  background: #66a6ff !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(102,166,255,0.3) !important;
}

.tab-inactive {
  background: transparent !important;
  color: #999 !important;
  font-weight: 500;
}

/* 分类网格 */
.category-section {
  margin: 18px 18px 0 18px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 18px 0;
  justify-items: center;
}

.cat-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  user-select: none;
  transition: all 0.18s cubic-bezier(.4,2,.6,1);
}

.cat-icon-wrap {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
  font-size: 28px;
  color: #b0b0b0;
  border: 2px solid transparent;
  transition: all 0.18s cubic-bezier(.4,2,.6,1);
}

.cat-icon-emoji {
  font-size: 24px;
}

.cat-btn.selected .cat-icon-wrap {
  background: #fff;
  transition: all 0.3s ease;
}

/* 主题分类选中样式 */
.theme-modern .cat-btn.selected .cat-icon-wrap {
  border: 2px solid #667eea;
  color: #667eea;
  box-shadow: 0 2px 12px rgba(102,126,234,0.3);
}

.theme-warm .cat-btn.selected .cat-icon-wrap {
  border: 2px solid #ff9a9e;
  color: #ff9a9e;
  box-shadow: 0 2px 12px rgba(255,154,158,0.3);
}

.theme-nature .cat-btn.selected .cat-icon-wrap {
  border: 2px solid #a8edea;
  color: #a8edea;
  box-shadow: 0 2px 12px rgba(168,237,234,0.3);
}

.theme-elegant .cat-btn.selected .cat-icon-wrap {
  border: 2px solid #d299c2;
  color: #d299c2;
  box-shadow: 0 2px 12px rgba(210,153,194,0.3);
}

.theme-ocean .cat-btn.selected .cat-icon-wrap {
  border: 2px solid #66a6ff;
  color: #66a6ff;
  box-shadow: 0 2px 12px rgba(102,166,255,0.3);
}

.cat-label {
  font-size: 14px;
  color: #718096;
  margin-top: 2px;
  transition: color 0.18s;
}

.cat-btn.selected .cat-label {
  font-weight: bold;
  transition: color 0.3s ease;
}

/* 主题分类标签选中样式 */
.theme-modern .cat-btn.selected .cat-label {
  color: #667eea;
}

.theme-warm .cat-btn.selected .cat-label {
  color: #ff9a9e;
}

.theme-nature .cat-btn.selected .cat-label {
  color: #a8edea;
}

.theme-elegant .cat-btn.selected .cat-label {
  color: #d299c2;
}

.theme-ocean .cat-btn.selected .cat-label {
  color: #66a6ff;
}

/* 金额显示区域 */
.amount-section {
  text-align: center;
  margin: 18px 0 0 0;
}

.amount-value {
  font-size: 44px;
  font-weight: bold;
  color: #222;
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  box-shadow: 0 2px 12px rgba(102,126,234,0.08);
  padding: 18px 0;
  margin: 0 auto 8px auto;
  width: 80%;
  cursor: pointer;
  transition: box-shadow 0.2s;
  letter-spacing: 2px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-value:active {
  box-shadow: 0 4px 24px rgba(102,126,234,0.18);
}
/* 信息卡片行 */
.card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 18px 18px 0 18px;
}

.info-card {
  flex: 1;
  background: rgba(255,255,255,0.9);
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  padding: 10px 0;
  margin: 0 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  border: 1px solid rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
  transition: box-shadow 0.18s;
}

.info-card-label {
  font-size: 12px;
  color: #718096;
}

.info-card-value {
  font-size: 15px;
  color: #2d3748;
  font-weight: 600;
  margin-top: 2px;
}

/* 保存按钮 */
.save-btn {
  position: fixed;
  left: 24px;
  right: 24px;
  bottom: 80px;
  border-radius: 24px;
  font-size: 18px;
  color: #fff;
  font-weight: 700;
  height: 48px;
  z-index: 100;
  border: none;
  transition: all 0.3s ease;
}

/* 主题保存按钮样式 */
.theme-modern .save-btn {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 24px rgba(102,126,234,0.3);
}

.theme-warm .save-btn {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
  box-shadow: 0 4px 24px rgba(255,154,158,0.3);
}

.theme-nature .save-btn {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4px 24px rgba(168,237,234,0.3);
}

.theme-elegant .save-btn {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%);
  box-shadow: 0 4px 24px rgba(210,153,194,0.3);
}

.theme-ocean .save-btn {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%);
  box-shadow: 0 4px 24px rgba(102,166,255,0.3);
}
/* 重复样式已在前面定义，此处移除 */

/* 键盘弹窗样式 */
.custom-keyboard-popup {
  min-height: 420px !important;
  z-index: 9999 !important;
  border-radius: 24px 24px 0 0 !important;
  box-shadow: 0 -4px 32px rgba(0,0,0,0.10) !important;
}

.keyboard-popup-content {
  padding: 20px 0 8px 0;
}

.keyboard-header {
  text-align: center;
  margin-bottom: 20px;
}

.keyboard-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.keyboard-amount {
  font-size: 32px;
  font-weight: bold;
  color: #222;
}

/* 备注弹窗样式 */
.remark-popup {
  padding: 20px;
}

.popup-header {
  text-align: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.popup-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

/* 记账对话框样式 */
.record-dialog-popup {
  min-height: 420px !important;
  z-index: 9999 !important;
  border-radius: 24px 24px 0 0 !important;
  box-shadow: 0 -4px 32px rgba(0,0,0,0.10) !important;
}

.record-dialog {
  background: #fff;
  padding: 18px 0 8px 0;
  border-radius: 24px 24px 0 0;
}

.dialog-category-header {
  text-align: center;
  padding: 18px 0 0 0;
}

.dialog-cat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px auto;
  transition: all 0.3s ease;
}

/* 主题对话框分类图标样式 */
.theme-modern .dialog-cat-icon {
  border: 2px solid #667eea;
  color: #667eea;
}

.theme-warm .dialog-cat-icon {
  border: 2px solid #ff9a9e;
  color: #ff9a9e;
}

.theme-nature .dialog-cat-icon {
  border: 2px solid #a8edea;
  color: #a8edea;
}

.theme-elegant .dialog-cat-icon {
  border: 2px solid #d299c2;
  color: #d299c2;
}

.theme-ocean .dialog-cat-icon {
  border: 2px solid #66a6ff;
  color: #66a6ff;
}

.dialog-cat-icon .cat-icon-emoji {
  font-size: 24px;
}

.dialog-cat-name {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  transition: color 0.3s ease;
}

/* 主题对话框分类名称样式 */
.theme-modern .dialog-cat-name {
  color: #667eea;
}

.theme-warm .dialog-cat-name {
  color: #ff9a9e;
}

.theme-nature .dialog-cat-name {
  color: #a8edea;
}

.theme-elegant .dialog-cat-name {
  color: #d299c2;
}

.theme-ocean .dialog-cat-name {
  color: #66a6ff;
}

.dialog-amount-section {
  text-align: center;
  margin: 16px 0 0 0;
}

.dialog-amount-value {
  font-size: 44px;
  font-weight: bold;
  color: #222;
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  box-shadow: 0 2px 12px rgba(102,126,234,0.08);
  padding: 18px 0;
  margin: 0 auto 8px auto;
  width: 80%;
  cursor: pointer;
  transition: box-shadow 0.2s;
  letter-spacing: 2px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-amount-value:active {
  box-shadow: 0 4px 24px rgba(102,126,234,0.18);
}

.dialog-remark-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 18px 0 18px;
}

.dialog-custom-keyboard {
  width: 100%;
  margin: 0 auto;
  padding: 0 0 8px 0;
  background: #f7f7fa;
  border-radius: 18px 18px 0 0;
  box-shadow: 0 -2px 16px rgba(0,0,0,0.06);
  user-select: none;
}

/* 日期时间选择器弹窗样式 */
.datetime-picker-popup {
  padding: 20px;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.popup-header {
  text-align: center;
  margin-bottom: 20px;
  width: 100%;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.picker-section {
  margin: 20px 0;
  width: 100%;
  box-sizing: border-box;
}

.picker-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
  display: block;
}

.date-picker,
.time-picker {
  margin: 8px 0;
  width: 100%;
}

.picker-display {
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  color: #333;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  display: block;
}

.picker-display:active {
  background: #e8e8e8;
  border-color: #667eea;
}

.popup-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  width: 100%;
  box-sizing: border-box;
}

/* 确保弹窗不会水平滚动 */
.van-popup {
  max-width: 100vw !important;
  overflow-x: hidden !important;
}

.van-popup__content {
  max-width: 100% !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

/* 防止picker组件溢出 */
picker {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

/* 确保所有文本不会溢出 */
.datetime-picker-popup text,
.datetime-picker-popup view {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.dialog-custom-keyboard .keyboard-row {
  display: flex;
  width: 100%;
  margin-bottom: 8px;
}

.dialog-custom-keyboard .key {
  flex: 1;
  margin: 0 6px;
  height: 48px;
  background: #fff;
  border: none;
  border-radius: 12px;
  font-size: 20px;
  color: #222;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  transition: background 0.15s;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  cursor: pointer;
}

.dialog-custom-keyboard .key:active {
  background: #f0e9d2;
}

/* 今天按钮特殊样式 */
.dialog-custom-keyboard .key-today {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
  color: #ffffff !important;
  flex-direction: column;
  font-size: 12px;
  gap: 2px;
  font-weight: 500;
}

.dialog-custom-keyboard .key-today:active {
  background: linear-gradient(90deg, #5a6fd8 0%, #6a4190 100%) !important;
}

.dialog-custom-keyboard .key-confirm {
  
  font-weight: bold;
  font-size: 18px;
  border-radius: 12px;
  transition: all 0.3s ease !important;
}

/* 主题对话框键盘确认按钮样式 */
.theme-modern .dialog-custom-keyboard .key-confirm {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
}

.theme-warm .dialog-custom-keyboard .key-confirm {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%) !important;
}

.theme-nature .dialog-custom-keyboard .key-confirm {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%) !important;
}

.theme-elegant .dialog-custom-keyboard .key-confirm {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%) !important;
}

.theme-ocean .dialog-custom-keyboard .key-confirm {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%) !important;
}

.dialog-custom-keyboard .key:disabled,
.dialog-custom-keyboard .key-confirm:disabled,
.dialog-custom-keyboard .key-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.custom-keyboard {
  margin: 48rpx 32rpx 0 32rpx;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.10);
  padding: 24rpx 0 0 0;
}
.keyboard-row {
  display: flex;
  width: 100%;
  margin-bottom: 18rpx;
}
.key {
  flex: 1;
  margin: 0 8rpx;
  height: 48px;
  background: #f7f7fa;
  border: none;
  border-radius: 12px;
  font-size: 20px;
  color: #222;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  cursor: pointer;
  transition: background 0.15s;
}
.key:active {
  background: #f0e9d2;
}
.key-confirm {
  color: #fff;
  font-weight: bold;
  font-size: 18px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

/* 主题键盘确认按钮样式 */
.theme-modern .key-confirm {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(102,126,234,0.3);
}

.theme-warm .key-confirm {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
  box-shadow: 0 2px 8px rgba(255,154,158,0.3);
}

.theme-nature .key-confirm {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 2px 8px rgba(168,237,234,0.3);
}

.theme-elegant .key-confirm {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%);
  box-shadow: 0 2px 8px rgba(210,153,194,0.3);
}

.theme-ocean .key-confirm {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%);
  box-shadow: 0 2px 8px rgba(102,166,255,0.3);
}
.key[disabled], .key-confirm[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.category-popup {
  padding: 32rpx 0 48rpx 0;
  background: #fff;
  border-radius: 24px 24px 0 0;
  min-height: 320rpx;
}
.selected-cat {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12rpx;
  gap: 12rpx;
}
.selected-cat .cat-icon-wrap {
  width: 32px;
  height: 32px;
  font-size: 20px;
  margin-bottom: 0;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 主题选中分类图标样式 */
.theme-modern .selected-cat .cat-icon-wrap {
  border: 2px solid #667eea;
  color: #667eea;
}

.theme-warm .selected-cat .cat-icon-wrap {
  border: 2px solid #ff9a9e;
  color: #ff9a9e;
}

.theme-nature .selected-cat .cat-icon-wrap {
  border: 2px solid #a8edea;
  color: #a8edea;
}

.theme-elegant .selected-cat .cat-icon-wrap {
  border: 2px solid #d299c2;
  color: #d299c2;
}

.theme-ocean .selected-cat .cat-icon-wrap {
  border: 2px solid #66a6ff;
  color: #66a6ff;
}

.selected-cat .cat-label {
  font-size: 15px;
  font-weight: bold;
  transition: color 0.3s ease;
}

/* 主题选中分类标签样式 */
.theme-modern .selected-cat .cat-label {
  color: #667eea;
}

.theme-warm .selected-cat .cat-label {
  color: #ff9a9e;
}

.theme-nature .selected-cat .cat-label {
  color: #a8edea;
}

.theme-elegant .selected-cat .cat-label {
  color: #d299c2;
}

.theme-ocean .selected-cat .cat-label {
  color: #66a6ff;
}

.input-popup {
  padding: 32rpx 0 48rpx 0;
  background: #fff;
  border-radius: 24px 24px 0 0;
  min-height: 320rpx;
}
.popup-cat-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}
.input-popup .cat-icon-wrap {
  width: 32px;
  height: 32px;
  font-size: 20px;
  margin-bottom: 0;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 主题输入弹窗分类图标样式 */
.theme-modern .input-popup .cat-icon-wrap {
  border: 2px solid #667eea;
  color: #667eea;
}

.theme-warm .input-popup .cat-icon-wrap {
  border: 2px solid #ff9a9e;
  color: #ff9a9e;
}

.theme-nature .input-popup .cat-icon-wrap {
  border: 2px solid #a8edea;
  color: #a8edea;
}

.theme-elegant .input-popup .cat-icon-wrap {
  border: 2px solid #d299c2;
  color: #d299c2;
}

.theme-ocean .input-popup .cat-icon-wrap {
  border: 2px solid #66a6ff;
  color: #66a6ff;
}

.input-popup .cat-label {
  font-size: 15px;
  font-weight: bold;
  transition: color 0.3s ease;
}

/* 主题输入弹窗分类标签样式 */
.theme-modern .input-popup .cat-label {
  color: #667eea;
}

.theme-warm .input-popup .cat-label {
  color: #ff9a9e;
}

.theme-nature .input-popup .cat-label {
  color: #a8edea;
}

.theme-elegant .input-popup .cat-label {
  color: #d299c2;
}

.theme-ocean .input-popup .cat-label {
  color: #66a6ff;
}
.input-popup .amount-section {
  margin: 24rpx 0 0 0;
}
.input-popup .amount-value {
  font-size: 44px;
  font-weight: bold;
  color: #222;
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  box-shadow: 0 2px 12px rgba(102,126,234,0.08);
  padding: 18px 0;
  margin: 0 auto 8px auto;
  width: 80%;
  letter-spacing: 2px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
} 
