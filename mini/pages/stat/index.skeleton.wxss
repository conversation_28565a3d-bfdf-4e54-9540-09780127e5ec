/* 统计分析页骨架屏样式 */
.skeleton-container {
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.skeleton-title {
  width: 100px;
  height: 24px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 20px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.skeleton-tab {
  width: 80px;
  height: 36px;
  background: #e2e8f0;
  border-radius: 18px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-time-types {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
}

.skeleton-time-type {
  width: 60px;
  height: 32px;
  background: #e2e8f0;
  border-radius: 16px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-chart-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.skeleton-chart-title {
  width: 120px;
  height: 18px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 16px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-chart-area {
  margin-bottom: 16px;
}

.skeleton-chart-placeholder {
  width: 100%;
  height: 140px;
  background: #e2e8f0;
  border-radius: 8px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-chart-summary {
  display: flex;
  justify-content: space-between;
}

.skeleton-summary-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.skeleton-category-list {
  background: white;
  border-radius: 16px;
  padding: 16px;
}

.skeleton-category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-category-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-dot {
  width: 12px;
  height: 12px;
  background: #e2e8f0;
  border-radius: 50%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-category-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.skeleton-text {
  background: #e2e8f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-text-xs { width: 40px; height: 12px; }
.skeleton-text-sm { width: 60px; height: 14px; }
.skeleton-text-md { width: 80px; height: 16px; }
.skeleton-text-lg { width: 70px; height: 18px; }

@keyframes skeleton-loading {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
