<!-- 联系我页面 -->
<view class="contact-root theme-{{currentTheme}}">
  <!-- 顶部导航 -->
  <view class="contact-header">
    <view class="header-bg"></view>
    <view class="header-content">
      <view class="contact-title">联系我们</view>
      <view class="contact-subtitle">随时为您提供帮助</view>
    </view>
  </view>

  <!-- 主要内容 -->
  <view class="contact-content">
    
    <!-- 公众号信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="card-icon">📱</view>
        <view class="card-title">微信公众号</view>
      </view>
      <view class="card-body">
        <view class="account-name">{{contactInfo.publicAccount}}</view>
        <view class="account-desc">{{contactInfo.description}}</view>
        
        <!-- 功能特色 -->
        <view class="features-list">
          <view class="feature-item" wx:for="{{contactInfo.features}}" wx:key="index">
            <text>{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 二维码卡片 -->
    <view class="qr-card">
      <view class="card-header">
        <view class="card-icon">📷</view>
        <view class="card-title">扫码关注</view>
      </view>
      <view class="qr-container">
        <view class="qr-placeholder" bindtap="onPreviewQRCode" bindlongpress="onSaveQRCode">
          <image class="qr-image" src="/images/wechat-qr.png" mode="aspectFit" />
          <view class="qr-tip">点击查看大图 · 长按保存</view>
        </view>
        <view class="qr-actions">
          <button class="action-btn primary" bindtap="onSaveQRCode">
            <text class="btn-icon">💾</text>
            <text>保存二维码</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 微信号卡片 -->
    <view class="wechat-card">
      <view class="card-header">
        <view class="card-icon">💬</view>
        <view class="card-title">微信号</view>
      </view>
      <view class="wechat-info">
        <view class="wechat-id">{{contactInfo.wechatId}}</view>
        <button class="copy-btn" bindtap="onCopyWechat">
          <text class="btn-icon">📋</text>
          <text>复制</text>
        </button>
      </view>
      <view class="wechat-tip">复制微信号，在微信中搜索添加</view>
    </view>

    <!-- 联系方式说明 -->
    <view class="contact-tips">
      <view class="tips-title">💡 联系方式</view>
      <view class="tips-list">
        <view class="tip-item">
          <text class="tip-icon">🔸</text>
          <text class="tip-text">关注公众号获取最新功能更新</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">🔸</text>
          <text class="tip-text">添加微信号进行一对一咨询</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">🔸</text>
          <text class="tip-text">遇到问题可随时联系我们</text>
        </view>
      </view>
    </view>

    <!-- 留言反馈卡片 -->
    <view class="feedback-card">
      <view class="card-header">
        <view class="card-icon">💭</view>
        <view class="card-title">留言反馈</view>
      </view>
      <view class="feedback-content">
        <view class="feedback-desc">有问题或建议？欢迎给我们留言</view>
        <button class="feedback-btn" bindtap="onShowFeedback">
          <text class="btn-icon">✍️</text>
          <text>写留言</text>
        </button>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view class="share-section">
      <button class="share-btn" open-type="share">
        <text class="btn-icon">📤</text>
        <text>分享给朋友</text>
      </button>
    </view>

  </view>
</view>

<!-- 留言弹窗 -->
<view class="feedback-popup {{showFeedback ? 'show' : ''}}">
  <view class="popup-mask" bindtap="onCloseFeedback"></view>
  <view class="feedback-popup-content" catchtap="">
    <view class="popup-header">
      <view class="popup-title">留言反馈</view>
      <view class="popup-actions-top">
        <view class="btn-cancel-top" bind:tap="onCloseFeedback">取消</view>
        <view class="btn-confirm-top" bind:tap="onSubmitFeedback">提交</view>
      </view>
    </view>

    <view class="feedback-form" catchtap="">
      <!-- 反馈类型选择 -->
      <view class="form-group" catchtap="">
        <view class="form-label">反馈类型</view>
        <view class="feedback-types">
          <view
            class="type-item {{feedbackType === item.value ? 'active' : ''}}"
            wx:for="{{feedbackTypes}}"
            wx:key="value"
            data-type="{{item.value}}"
            catchtap="onSelectType"
          >
            <text class="type-icon">{{item.icon}}</text>
            <text class="type-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="form-group" catchtap="">
        <view class="form-label">联系方式（可选）</view>
        <input
          class="form-input"
          placeholder="微信号/手机号，方便我们回复您"
          value="{{contactWay}}"
          bind:input="onContactChange"
          bind:focus="onInputFocus"
          bind:blur="onInputBlur"
          maxlength="50"
          catchtap=""
        />
      </view>

      <!-- 留言内容 -->
      <view class="form-group" catchtap="">
        <view class="form-label">留言内容 *</view>
        <textarea
          class="form-textarea"
          placeholder="请详细描述您的问题或建议..."
          value="{{feedbackContent}}"
          bind:input="onContentChange"
          bind:focus="onInputFocus"
          bind:blur="onInputBlur"
          maxlength="500"
          show-confirm-bar="{{false}}"
          catchtap=""
        />
        <view class="char-count">{{feedbackContent.length}}/500</view>
      </view>
    </view>
  </view>
</view>
