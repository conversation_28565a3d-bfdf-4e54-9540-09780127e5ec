/* 联系我页面样式 */
page {
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* 主题背景 */
.theme-modern page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.theme-warm page {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.theme-nature page {
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
}

.theme-elegant page {
  background: linear-gradient(135deg, #f9f0ff 0%, #ede7f6 100%);
}

.theme-ocean page {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.contact-root {
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 顶部头部 */
.contact-header {
  height: 200rpx;
  position: relative;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0 0 50rpx 50rpx;
  transition: background 0.3s ease;
}

/* 主题头部背景 */
.theme-modern .header-bg {
  background: linear-gradient(135deg, rgba(102,126,234,0.9) 0%, rgba(118,75,162,0.9) 100%);
}

.theme-warm .header-bg {
  background: linear-gradient(135deg, rgba(255,154,158,0.9) 0%, rgba(254,207,239,0.9) 100%);
}

.theme-nature .header-bg {
  background: linear-gradient(135deg, rgba(168,237,234,0.9) 0%, rgba(254,214,227,0.9) 100%);
}

.theme-elegant .header-bg {
  background: linear-gradient(135deg, rgba(210,153,194,0.9) 0%, rgba(254,249,215,0.9) 100%);
}

.theme-ocean .header-bg {
  background: linear-gradient(135deg, rgba(137,247,254,0.9) 0%, rgba(102,166,255,0.9) 100%);
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.contact-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.contact-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 内容区域 */
.contact-content {
  padding: 0 32rpx;
}

/* 通用卡片样式 */
.info-card, .qr-card, .wechat-card, .feedback-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.info-card:active, .qr-card:active, .wechat-card:active, .feedback-card:active {
  transform: translateY(4rpx);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
}

/* 公众号信息 */
.account-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 16rpx;
}

.account-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
  line-height: 1.6;
}

.features-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.feature-item {
  font-size: 26rpx;
  color: #555;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  text-align: center;
}

/* 二维码样式 */
.qr-container {
  text-align: center;
}

.qr-placeholder {
  display: inline-block;
  position: relative;
  margin-bottom: 32rpx;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.qr-placeholder:active {
  transform: scale(0.98);
}

.qr-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 24rpx;
  border: 4rpx solid #f0f0f0;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.qr-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

.qr-actions {
  display: flex;
  justify-content: center;
}

/* 微信号样式 */
.wechat-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.wechat-id {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  font-family: 'Monaco', 'Menlo', monospace;
}

.wechat-tip {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 按钮样式 */
.action-btn, .copy-btn, .share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(120deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.copy-btn {
  background: #667eea;
  color: white;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}

.share-btn {
  background: linear-gradient(120deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
  width: 100%;
  margin-top: 16rpx;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.action-btn:active, .copy-btn:active, .share-btn:active {
  transform: scale(0.98);
}

/* 联系提示 */
.contact-tips {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-icon {
  margin-right: 16rpx;
  margin-top: 4rpx;
  color: #667eea;
}

.tip-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
  flex: 1;
}

/* 留言反馈卡片 */
.feedback-content {
  text-align: center;
}

.feedback-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.feedback-btn {
  background: linear-gradient(120deg, #52c41a 0%, #73d13d 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.feedback-btn:active {
  transform: scale(0.98);
}

/* 留言弹窗 */
.feedback-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.feedback-popup.show {
  opacity: 1;
  visibility: visible;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.feedback-popup-content {
  width: 100%;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx;
  padding-bottom: 80rpx; /* 增加底部内边距，避免被键盘遮挡 */
  max-height: 85vh; /* 增加最大高度 */
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}

.feedback-popup.show .feedback-popup-content {
  transform: translateY(0);
}

/* 弹窗头部 */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
}

.popup-actions-top {
  display: flex;
  gap: 16rpx;
}

.btn-cancel-top, .btn-confirm-top {
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-cancel-top {
  color: #666;
  background: #f8f9fa;
}

.btn-cancel-top:active {
  background: #e9ecef;
}

.btn-confirm-top {
  color: white;
  background: #52c41a;
}

.btn-confirm-top:active {
  background: #389e0d;
}

/* 表单样式 */
.feedback-form {
  padding: 0;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 20rpx;
}

/* 反馈类型选择 */
.feedback-types {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.type-item.active {
  background: rgba(82, 196, 26, 0.1);
  border-color: #52c41a;
}

.type-item:active {
  transform: scale(0.98);
}

.type-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.type-text {
  font-size: 24rpx;
  color: #555;
  font-weight: 500;
}

.type-item.active .type-text {
  color: #52c41a;
}

/* 表单输入 */
.form-input, .form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 28rpx;
  background: #fafafa;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input {
  height: 88rpx;
  line-height: 40rpx;
  padding: 24rpx 32rpx; /* 增加左右内边距 */
}

.form-input:focus, .form-textarea:focus {
  border-color: #52c41a;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
}

.form-textarea {
  height: 200rpx;
  resize: none;
  line-height: 1.5;
  padding: 24rpx 32rpx; /* 增加左右内边距 */
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 分享区域 */
.share-section {
  padding: 32rpx 0;
}
