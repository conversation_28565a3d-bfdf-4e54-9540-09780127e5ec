// pages/contact/index.js
Page({
    data: {
        currentTheme: 'modern',
        contactInfo: {
            wechatId: 'huang8023wei',
            publicAccount: '简易记账助手',
            description: '专注于个人财务管理，让记账更简单',
            features: [
                '💰 智能记账提醒',
                '📊 数据分析报告',
                '💡 理财小贴士',
                '🎯 预算管理建议'
            ]
        },

        // 留言反馈相关
        showFeedback: false,
        feedbackType: 'suggestion',
        feedbackTypes: [
            { value: 'bug', label: '问题反馈', icon: '🐛' },
            { value: 'suggestion', label: '功能建议', icon: '💡' },
            { value: 'question', label: '使用疑问', icon: '❓' },
            { value: 'other', label: '其他', icon: '💬' }
        ],
        contactWay: '',
        feedbackContent: ''
    },

    onLoad() {
        this.loadTheme();
    },

    onShow() {
        this.loadTheme();
    },

    /**
     * 加载主题
     */
    loadTheme() {
        const app = getApp();
        const currentTheme = app.globalData.currentTheme;
        this.setData({ currentTheme });
    },

    /**
     * 主题变更回调
     */
    onThemeChange(themeId) {
        this.setData({ currentTheme: themeId });
    },

    /**
     * 复制微信号
     */
    onCopyWechat() {
        wx.setClipboardData({
            data: this.data.contactInfo.wechatId,
            success: () => {
                wx.showToast({
                    title: '微信号已复制',
                    icon: 'success'
                });
            }
        });
    },

    /**
     * 保存二维码到相册
     */
    onSaveQRCode() {
        // 显示保存提示
        wx.showModal({
            title: '保存二维码',
            content: '是否保存二维码到相册？',
            success: (res) => {
                if (res.confirm) {
                    // 用户确认保存
                    wx.saveImageToPhotosAlbum({
                        filePath: '/images/wechat-qr.png',
                        success: () => {
                            wx.showToast({
                                title: '保存成功',
                                icon: 'success'
                            });
                        },
                        fail: (err) => {
                            console.error('保存失败:', err);
                            if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
                                wx.showModal({
                                    title: '需要授权',
                                    content: '请在设置中允许访问相册权限',
                                    showCancel: true,
                                    confirmText: '去设置',
                                    success: (modalRes) => {
                                        if (modalRes.confirm) {
                                            wx.openSetting();
                                        }
                                    }
                                });
                            } else {
                                wx.showToast({
                                    title: '保存失败',
                                    icon: 'none'
                                });
                            }
                        }
                    });
                }
            }
        });
    },

    /**
     * 预览二维码
     */
    onPreviewQRCode() {
        wx.previewImage({
            urls: ['/images/wechat-qr.png'],
            current: '/images/wechat-qr.png',
            success: () => {
                console.log('二维码预览成功');
            },
            fail: (err) => {
                console.error('二维码预览失败:', err);
                wx.showToast({
                    title: '图片加载失败',
                    icon: 'none'
                });
            }
        });
    },

    /**
     * 分享给朋友
     */
    onShareAppMessage() {
        return {
            title: '简易记账 - 让记账更简单',
            path: '/pages/home/<USER>',
            imageUrl: '/images/share-cover.png'
        };
    },

    /**
     * 显示留言弹窗
     */
    onShowFeedback() {
        this.setData({
            showFeedback: true,
            feedbackType: 'suggestion',
            contactWay: '',
            feedbackContent: ''
        });
    },

    /**
     * 关闭留言弹窗
     */
    onCloseFeedback() {
        this.setData({ showFeedback: false });
    },

    /**
     * 选择反馈类型
     */
    onSelectType(e) {
        const type = e.currentTarget.dataset.type;
        this.setData({ feedbackType: type });
    },

    /**
     * 联系方式输入
     */
    onContactChange(e) {
        this.setData({ contactWay: e.detail.value });
    },

    /**
     * 留言内容输入
     */
    onContentChange(e) {
        this.setData({ feedbackContent: e.detail.value });
    },

    /**
     * 输入框聚焦
     */
    onInputFocus(e) {
        // 可以在这里添加聚焦时的处理逻辑
        console.log('输入框聚焦');
    },

    /**
     * 输入框失焦
     */
    onInputBlur(e) {
        // 可以在这里添加失焦时的处理逻辑
        console.log('输入框失焦');
    },

    /**
     * 提交留言
     */
    onSubmitFeedback() {
        const { feedbackType, feedbackContent, contactWay } = this.data;

        // 验证必填项
        if (!feedbackContent.trim()) {
            wx.showToast({
                title: '请输入留言内容',
                icon: 'none'
            });
            return;
        }

        // 显示提交中
        wx.showLoading({ title: '提交中...' });

        // 构造留言数据
        const feedbackData = {
            type: feedbackType,
            content: feedbackContent.trim(),
            contact: contactWay.trim(),
            timestamp: new Date().toISOString(),
            userInfo: this.getUserInfo()
        };

        // 保存到本地存储（实际项目中应该提交到服务器）
        this.saveFeedback(feedbackData);
    },

    /**
     * 获取用户信息
     */
    getUserInfo() {
        const userInfo = wx.getStorageSync('userInfo') || {};
        return {
            nickname: userInfo.nickname || '匿名用户',
            timestamp: new Date().toISOString()
        };
    },

    /**
     * 保存留言到本地
     */
    saveFeedback(feedbackData) {
        try {
            // 获取现有留言
            const feedbacks = wx.getStorageSync('feedbacks') || [];

            // 添加新留言
            feedbacks.unshift({
                id: Date.now(),
                ...feedbackData
            });

            // 只保留最近50条留言
            if (feedbacks.length > 50) {
                feedbacks.splice(50);
            }

            // 保存到本地存储
            wx.setStorageSync('feedbacks', feedbacks);

            wx.hideLoading();

            // 显示成功提示
            wx.showToast({
                title: '提交成功',
                icon: 'success',
                duration: 2000
            });

            // 关闭弹窗
            this.setData({ showFeedback: false });

            // 显示后续提示
            setTimeout(() => {
                wx.showModal({
                    title: '感谢您的反馈',
                    content: '我们会认真查看每一条留言，如有需要会通过您提供的联系方式回复。',
                    showCancel: false,
                    confirmText: '知道了'
                });
            }, 2500);

        } catch (error) {
            wx.hideLoading();
            wx.showToast({
                title: '提交失败，请重试',
                icon: 'none'
            });
            console.error('保存留言失败:', error);
        }
    }
});
