/* 我的页面骨架屏样式 */
.skeleton-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.skeleton-header {
  background: linear-gradient(120deg, #667eea 0%, #764ba2 100%);
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  position: relative;
  border-radius: 0 0 32px 32px;
}

.skeleton-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255,255,255,0.3);
  border-radius: 50%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.skeleton-stats-card {
  background: white;
  margin: -40px 16px 16px 16px;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.skeleton-stats-grid {
  display: flex;
  justify-content: space-around;
}

.skeleton-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.skeleton-menu-card {
  background: white;
  margin: 0 16px;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.skeleton-menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-menu-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.skeleton-icon {
  width: 24px;
  height: 24px;
  background: #e2e8f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-arrow {
  width: 16px;
  height: 16px;
  background: #e2e8f0;
  border-radius: 2px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-text {
  background: rgba(255,255,255,0.3);
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-header .skeleton-text {
  background: rgba(255,255,255,0.3);
}

.skeleton-menu-card .skeleton-text {
  background: #e2e8f0;
}

.skeleton-text-sm { width: 80px; height: 14px; }
.skeleton-text-md { width: 100px; height: 16px; }
.skeleton-text-lg { width: 120px; height: 18px; }
.skeleton-text-xl { width: 60px; height: 24px; }

@keyframes skeleton-loading {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
