// pages/mine/index.js
Page({
    data: {
        nickname: '小账本',
        signature: '让记账更简单',
        avatar: '👤',
        // { id: 1, label: '账户安全', value: '已绑定手机号', icon: 'shield-o', iconText: '🛡️' }
        settings: [
            { id: 2, label: '主题风格', value: '现代科技感', icon: 'brush-o', iconText: '🎨' },
            { id: 3, label: '联系我', value: '公众号 & 微信', icon: 'chat-o', iconText: '💬' },
            { id: 4, label: '关于', value: 'v1.0.0', icon: 'info-o', iconText: 'ℹ️' },
        ],

        // 弹窗状态
        showNickPopup: false,
        showSignaturePopup: false,
        showThemePopup: false,

        // 输入状态
        nickInput: '',
        signatureInput: '',
        
        // 头像类型：只支持 'image'
        avatarType: 'image',
        avatarImage: '', // 上传的头像图片路径
        defaultAvatar: '👤', // 默认头像显示

        // 主题相关
        currentTheme: 'modern',
        themes: [
            {
                id: 'modern',
                name: '现代科技',
                desc: '蓝紫渐变，科技感十足',
                gradient: 'linear-gradient(120deg, #667eea 0%, #764ba2 100%)',
                primaryColor: '#667eea',
                icon: '🚀'
            },
            {
                id: 'warm',
                name: '温暖橙光',
                desc: '橙红渐变，温暖舒适',
                gradient: 'linear-gradient(120deg, #ff9a9e 0%, #fecfef 100%)',
                primaryColor: '#ff9a9e',
                icon: '🌅'
            },
            {
                id: 'nature',
                name: '自然绿意',
                desc: '绿色渐变，清新自然',
                gradient: 'linear-gradient(120deg, #a8edea 0%, #fed6e3 100%)',
                primaryColor: '#a8edea',
                icon: '🌿'
            },
            {
                id: 'elegant',
                name: '优雅紫调',
                desc: '紫色渐变，优雅高贵',
                gradient: 'linear-gradient(120deg, #d299c2 0%, #fef9d7 100%)',
                primaryColor: '#d299c2',
                icon: '💜'
            },
            {
                id: 'ocean',
                name: '海洋蓝调',
                desc: '蓝色渐变，深邃宁静',
                gradient: 'linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%)',
                primaryColor: '#66a6ff',
                icon: '🌊'
            }
        ]
    },

    onLoad() {
        this.loadTheme();
        this.loadUserInfo();
    },

    onShow() {
        this.loadTheme();
        this.loadUserInfo();
        // 同步自定义 tabbar 选中状态
        setTimeout(() => {
            if (typeof this.getTabBar === 'function' && this.getTabBar()) {
                this.getTabBar().setData({
                    selected: 3
                })
            }
        }, 100);
    },

    /**
     * 加载主题
     */
    loadTheme() {
        const app = getApp();
        const currentTheme = app.globalData.currentTheme;
        this.setData({ currentTheme });
    },

    /**
     * 加载用户信息
     */
    loadUserInfo() {
        const userInfo = wx.getStorageSync('userInfo') || {};
        const currentTheme = wx.getStorageSync('currentTheme') || 'modern';
        
        // 获取当前主题信息
        const currentThemeInfo = this.data.themes.find(t => t.id === currentTheme) || this.data.themes[0];
        
        // 更新设置项中的主题显示
        const settings = [...this.data.settings];
        const themeIndex = settings.findIndex(item => item.id === 2);
        if (themeIndex !== -1) {
            settings[themeIndex].value = currentThemeInfo.name;
        }
        
        // 处理头像显示逻辑
        const avatarImage = userInfo.avatarImage || '';
        const avatarType = avatarImage ? 'image' : 'default';
        
        this.setData({
            nickname: userInfo.nickname || '小账本',
            signature: userInfo.signature || '让记账更简单',
            avatar: avatarImage || '👤',
            avatarType: avatarType,
            avatarImage: avatarImage,
            currentTheme,
            settings
        });
    },

    saveUserInfo() {
        const userInfo = {
            nickname: this.data.nickname,
            signature: this.data.signature,
            avatarImage: this.data.avatarImage
        };
        wx.setStorageSync('userInfo', userInfo);
    },

    /**
     * 点击头像 - 直接触发上传
     */
    onAvatarTap() {
        this.onUploadAvatar();
    },

    /**
     * 上传头像图片
     */
    onUploadAvatar() {
        wx.showActionSheet({
            itemList: ['拍照', '从相册选择'],
            success: (res) => {
                const sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];
                this.chooseImage(sourceType);
            }
        });
    },

    /**
     * 选择图片
     */
    chooseImage(sourceType) {
        wx.chooseMedia({
            count: 1,
            mediaType: ['image'],
            sourceType: sourceType,
            maxDuration: 30,
            camera: 'back',
            success: (res) => {
                const tempFilePath = res.tempFiles[0].tempFilePath;
                const fileSize = res.tempFiles[0].size;
                
                // 检查文件大小（限制为5MB）
                if (fileSize > 5 * 1024 * 1024) {
                    wx.showToast({ title: '图片大小不能超过5MB', icon: 'none' });
                    return;
                }
                
                // 显示加载提示
                wx.showLoading({ title: '处理中...' });
                
                // 保存图片到本地
                this.saveAvatarImage(tempFilePath);
            },
            fail: (err) => {
                console.error('选择图片失败:', err);
                if (err.errMsg !== 'chooseMedia:fail cancel') {
                    wx.showToast({ title: '选择图片失败', icon: 'none' });
                }
            }
        });
    },

    /**
     * 保存头像图片
     */
    saveAvatarImage(tempFilePath) {
        // 先压缩图片
        wx.compressImage({
            src: tempFilePath,
            quality: 80,
            success: (compressRes) => {
                this.saveCompressedImage(compressRes.tempFilePath);
            },
            fail: (err) => {
                console.error('压缩图片失败:', err);
                // 压缩失败则使用原图
                this.saveCompressedImage(tempFilePath);
            }
        });
    },

    /**
     * 保存压缩后的图片
     */
    saveCompressedImage(tempFilePath) {
        // 获取文件管理器
        const fs = wx.getFileSystemManager();
        
        // 生成唯一文件名
        const timestamp = Date.now();
        const fileName = `avatar_${timestamp}.jpg`;
        const savedPath = `${wx.env.USER_DATA_PATH}/${fileName}`;
        
        // 保存文件到本地
        fs.saveFile({
            tempFilePath: tempFilePath,
            filePath: savedPath,
            success: (res) => {
                // 清理旧头像文件
                this.cleanOldAvatar();
                
                // 更新头像
                this.setData({
                    avatar: savedPath,
                    avatarType: 'image',
                    avatarImage: savedPath
                });
                
                // 保存到本地存储
                this.saveUserInfo();
                
                wx.hideLoading();
                wx.showToast({ title: '头像已更新', icon: 'success' });
            },
            fail: (err) => {
                console.error('保存头像失败:', err);
                wx.hideLoading();
                wx.showToast({ title: '保存头像失败', icon: 'none' });
            }
        });
    },

    /**
     * 清理旧头像文件
     */
    cleanOldAvatar() {
        const oldAvatarImage = this.data.avatarImage;
        if (oldAvatarImage && oldAvatarImage.includes(wx.env.USER_DATA_PATH)) {
            const fs = wx.getFileSystemManager();
            try {
                fs.unlinkSync(oldAvatarImage);
                console.log('已清理旧头像文件');
            } catch (err) {
                console.log('清理旧头像文件失败:', err);
            }
        }
    },

    /**
     * 点击昵称
     */
    onNickTap() {
        this.setData({
            showNickPopup: true,
            nickInput: this.data.nickname
        });
    },

    /**
     * 关闭昵称弹窗
     */
    onNickClose() {
        this.setData({ showNickPopup: false });
    },

    /**
     * 昵称输入
     */
    onNickChange(e) {
        this.setData({ nickInput: e.detail.value });
    },

    /**
     * 确认昵称修改
     */
    onNickConfirm() {
        const nickname = this.data.nickInput.trim();
        if (!nickname) {
            wx.showToast({ title: '昵称不能为空', icon: 'none' });
            return;
        }
        
        this.setData({
            nickname: nickname,
            showNickPopup: false
        }, () => {
            this.saveUserInfo();
            wx.showToast({ title: '昵称已更新', icon: 'success' });
        });
    },

    /**
     * 点击个性签名
     */
    onSignatureTap() {
        this.setData({
            showSignaturePopup: true,
            signatureInput: this.data.signature
        });
    },

    /**
     * 关闭个性签名弹窗
     */
    onSignatureClose() {
        this.setData({ showSignaturePopup: false });
    },

    /**
     * 个性签名输入
     */
    onSignatureChange(e) {
        this.setData({ signatureInput: e.detail.value });
    },

    /**
     * 确认个性签名修改
     */
    onSignatureConfirm() {
        const signature = this.data.signatureInput.trim();
        if (!signature) {
            wx.showToast({ title: '个性签名不能为空', icon: 'none' });
            return;
        }
        
        this.setData({
            signature: signature,
            showSignaturePopup: false
        }, () => {
            this.saveUserInfo();
            wx.showToast({ title: '个性签名已更新', icon: 'success' });
        });
    },

    /**
     * 设置项点击
     */
    onSettingTap(e) {
        const id = e.currentTarget.dataset.id;
        switch (id) {
            case 1:
                this.goAccountSecurity();
                break;
            case 2:
                this.goThemeSetting();
                break;
            case 3:
                this.goContact();
                break;
            case 4:
                this.goAbout();
                break;
        }
    },

    /**
     * 跳转到账户安全
     */
    goAccountSecurity() {
        wx.showToast({ title: '功能开发中', icon: 'none' });
    },

    /**
     * 跳转到联系我页面
     */
    goContact() {
        wx.navigateTo({
            url: '/pages/contact/index'
        });
    },

    /**
     * 打开主题选择弹窗
     */
    goThemeSetting() {
        this.setData({ showThemePopup: true });
    },

    /**
     * 关闭主题弹窗
     */
    onThemePopupClose() {
        this.setData({ showThemePopup: false });
    },

    /**
     * 选择主题
     */
    onThemeSelect(e) {
        const themeId = e.currentTarget.dataset.theme;
        const theme = this.data.themes.find(t => t.id === themeId);

        if (theme) {
            // 使用全局主题管理
            const app = getApp();
            app.setTheme(themeId);

            // 更新当前主题
            this.setData({ currentTheme: themeId });

            // 更新设置项显示
            const settings = [...this.data.settings];
            const themeIndex = settings.findIndex(item => item.id === 2);
            if (themeIndex !== -1) {
                settings[themeIndex].value = theme.name;
            }
            this.setData({ settings });

            // 关闭弹窗
            this.setData({ showThemePopup: false });

            // 显示成功提示
            wx.showToast({
                title: `已切换到${theme.name}`,
                icon: 'success'
            });
        }
    },

    /**
     * 主题变更回调
     */
    onThemeChange(themeId) {
        this.setData({ currentTheme: themeId });

        // 更新设置项显示
        const theme = this.data.themes.find(t => t.id === themeId);
        if (theme) {
            const settings = [...this.data.settings];
            const themeIndex = settings.findIndex(item => item.id === 2);
            if (themeIndex !== -1) {
                settings[themeIndex].value = theme.name;
            }
            this.setData({ settings });
        }
    },

    /**
     * 跳转到关于页面
     */
    goAbout() {
        wx.showModal({
            title: '关于简易记账',
            content: '版本：v1.0.0\n\n一款简洁易用的记账应用，帮助您轻松管理个人财务。',
            showCancel: false,
            confirmText: '知道了'
        });
    },

    /**
     * 长按关于项 - 进入留言管理（开发者功能）
     */
    onAboutLongPress() {
        wx.showModal({
            title: '开发者功能',
            content: '是否进入留言管理页面？',
            success: (res) => {
                if (res.confirm) {
                    wx.navigateTo({
                        url: '/pages/feedback/index'
                    });
                }
            }
        });
    }
});
