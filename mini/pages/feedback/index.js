// pages/feedback/index.js
Page({
    data: {
        currentTheme: 'modern',
        feedbacks: [],
        filterType: 'all',
        typeOptions: [
            { value: 'all', label: '全部', icon: '📋' },
            { value: 'bug', label: '问题反馈', icon: '🐛' },
            { value: 'suggestion', label: '功能建议', icon: '💡' },
            { value: 'question', label: '使用疑问', icon: '❓' },
            { value: 'other', label: '其他', icon: '💬' }
        ],
        filteredFeedbacks: [],
        showDetail: false,
        selectedFeedback: null
    },

    onLoad() {
        this.loadTheme();
        this.loadFeedbacks();
    },

    onShow() {
        this.loadTheme();
        this.loadFeedbacks();
    },

    /**
     * 加载主题
     */
    loadTheme() {
        const app = getApp();
        const currentTheme = app.globalData.currentTheme;
        this.setData({ currentTheme });
    },

    /**
     * 加载留言数据
     */
    loadFeedbacks() {
        const feedbacks = wx.getStorageSync('feedbacks') || [];
        this.setData({ feedbacks });
        this.filterFeedbacks();
    },

    /**
     * 筛选留言
     */
    filterFeedbacks() {
        const { feedbacks, filterType } = this.data;
        let filteredFeedbacks = feedbacks;

        if (filterType !== 'all') {
            filteredFeedbacks = feedbacks.filter(item => item.type === filterType);
        }

        this.setData({ filteredFeedbacks });
    },

    /**
     * 切换筛选类型
     */
    onFilterChange(e) {
        const type = e.currentTarget.dataset.type;
        this.setData({ filterType: type }, () => {
            this.filterFeedbacks();
        });
    },

    /**
     * 查看留言详情
     */
    onViewDetail(e) {
        const index = e.currentTarget.dataset.index;
        const feedback = this.data.filteredFeedbacks[index];
        this.setData({
            selectedFeedback: feedback,
            showDetail: true
        });
    },

    /**
     * 关闭详情弹窗
     */
    onCloseDetail() {
        this.setData({ showDetail: false });
    },

    /**
     * 删除留言
     */
    onDeleteFeedback() {
        const { selectedFeedback, feedbacks } = this.data;
        
        wx.showModal({
            title: '确认删除',
            content: '确定要删除这条留言吗？',
            success: (res) => {
                if (res.confirm) {
                    // 从数组中移除
                    const newFeedbacks = feedbacks.filter(item => item.id !== selectedFeedback.id);
                    
                    // 更新本地存储
                    wx.setStorageSync('feedbacks', newFeedbacks);
                    
                    // 更新页面数据
                    this.setData({ 
                        feedbacks: newFeedbacks,
                        showDetail: false 
                    }, () => {
                        this.filterFeedbacks();
                    });

                    wx.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });
                }
            }
        });
    },

    /**
     * 复制留言内容
     */
    onCopyContent() {
        const { selectedFeedback } = this.data;
        const content = `类型：${this.getTypeLabel(selectedFeedback.type)}\n联系方式：${selectedFeedback.contact || '未提供'}\n内容：${selectedFeedback.content}\n时间：${this.formatTime(selectedFeedback.timestamp)}`;
        
        wx.setClipboardData({
            data: content,
            success: () => {
                wx.showToast({
                    title: '已复制到剪贴板',
                    icon: 'success'
                });
            }
        });
    },

    /**
     * 获取类型标签
     */
    getTypeLabel(type) {
        const typeMap = {
            'bug': '问题反馈',
            'suggestion': '功能建议',
            'question': '使用疑问',
            'other': '其他'
        };
        return typeMap[type] || '未知';
    },

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) { // 1天内
            return `${Math.floor(diff / 3600000)}小时前`;
        } else {
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }
    },

    /**
     * 清空所有留言
     */
    onClearAll() {
        wx.showModal({
            title: '确认清空',
            content: '确定要清空所有留言吗？此操作不可恢复。',
            confirmColor: '#ff4d4f',
            success: (res) => {
                if (res.confirm) {
                    wx.setStorageSync('feedbacks', []);
                    this.setData({
                        feedbacks: [],
                        filteredFeedbacks: []
                    });
                    wx.showToast({
                        title: '已清空',
                        icon: 'success'
                    });
                }
            }
        });
    },

    /**
     * 主题变更回调
     */
    onThemeChange(themeId) {
        this.setData({ currentTheme: themeId });
    }
});
