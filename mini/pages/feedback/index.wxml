<!-- 留言管理页面 -->
<wxs module="utils">
  function formatTime(timestamp) {
    var date = getDate(timestamp);
    var now = getDate();
    var diff = now.getTime() - date.getTime();

    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      var hour = date.getHours();
      var minute = date.getMinutes();

      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;
      hour = hour < 10 ? '0' + hour : hour;
      minute = minute < 10 ? '0' + minute : minute;

      return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
    }
  }

  module.exports = {
    formatTime: formatTime
  };
</wxs>

<view class="feedback-root theme-{{currentTheme}}">
  <!-- 顶部导航 -->
  <view class="feedback-header">
    <view class="header-bg"></view>
    <view class="header-content">
      <view class="feedback-title">留言管理</view>
      <view class="feedback-subtitle">共 {{feedbacks.length}} 条留言</view>
    </view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <view 
          class="filter-item {{filterType === item.value ? 'active' : ''}}"
          wx:for="{{typeOptions}}"
          wx:key="value"
          data-type="{{item.value}}"
          bindtap="onFilterChange"
        >
          <text class="filter-icon">{{item.icon}}</text>
          <text class="filter-text">{{item.label}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 留言列表 -->
  <view class="feedback-content">
    <view wx:if="{{filteredFeedbacks.length === 0}}" class="empty-state">
      <view class="empty-icon">📭</view>
      <view class="empty-text">暂无留言</view>
      <view class="empty-desc">用户的反馈将在这里显示</view>
    </view>

    <view wx:else class="feedback-list">
      <view 
        class="feedback-item"
        wx:for="{{filteredFeedbacks}}"
        wx:key="id"
        data-index="{{index}}"
        bindtap="onViewDetail"
      >
        <view class="item-header">
          <view class="item-type">
            <text class="type-icon">{{item.type === 'bug' ? '🐛' : item.type === 'suggestion' ? '💡' : item.type === 'question' ? '❓' : '💬'}}</text>
            <text class="type-text">{{item.type === 'bug' ? '问题反馈' : item.type === 'suggestion' ? '功能建议' : item.type === 'question' ? '使用疑问' : '其他'}}</text>
          </view>
          <view class="item-time">{{utils.formatTime(item.timestamp)}}</view>
        </view>
        
        <view class="item-content">{{item.content}}</view>
        
        <view class="item-footer">
          <view class="item-user">
            <text class="user-icon">👤</text>
            <text class="user-name">{{item.userInfo.nickname}}</text>
          </view>
          <view wx:if="{{item.contact}}" class="item-contact">
            <text class="contact-icon">📞</text>
            <text class="contact-text">{{item.contact}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view wx:if="{{feedbacks.length > 0}}" class="action-section">
      <button class="clear-btn" bindtap="onClearAll">
        <text class="btn-icon">🗑️</text>
        <text>清空所有留言</text>
      </button>
    </view>
  </view>
</view>

<!-- 留言详情弹窗 -->
<view class="detail-popup {{showDetail ? 'show' : ''}}" bindtap="onCloseDetail">
  <view class="detail-popup-content" catchtap="">
    <view class="popup-header">
      <view class="popup-title">留言详情</view>
      <view class="popup-close" bindtap="onCloseDetail">✕</view>
    </view>
    
    <view wx:if="{{selectedFeedback}}" class="detail-content">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-title">基本信息</view>
        <view class="info-row">
          <text class="info-label">反馈类型：</text>
          <text class="info-value">{{selectedFeedback.type === 'bug' ? '🐛 问题反馈' : selectedFeedback.type === 'suggestion' ? '💡 功能建议' : selectedFeedback.type === 'question' ? '❓ 使用疑问' : '💬 其他'}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">提交时间：</text>
          <text class="info-value">{{selectedFeedback.timestamp}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">用户昵称：</text>
          <text class="info-value">{{selectedFeedback.userInfo.nickname}}</text>
        </view>
        <view wx:if="{{selectedFeedback.contact}}" class="info-row">
          <text class="info-label">联系方式：</text>
          <text class="info-value">{{selectedFeedback.contact}}</text>
        </view>
      </view>

      <!-- 留言内容 -->
      <view class="detail-section">
        <view class="section-title">留言内容</view>
        <view class="content-text">{{selectedFeedback.content}}</view>
      </view>

      <!-- 操作按钮 -->
      <view class="detail-actions">
        <button class="action-btn copy" bindtap="onCopyContent">
          <text class="btn-icon">📋</text>
          <text>复制内容</text>
        </button>
        <button class="action-btn delete" bindtap="onDeleteFeedback">
          <text class="btn-icon">🗑️</text>
          <text>删除留言</text>
        </button>
      </view>
    </view>
  </view>
</view>
