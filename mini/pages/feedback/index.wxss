/* 留言管理页面样式 */
page {
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* 主题背景 */
.theme-modern page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.theme-warm page {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.theme-nature page {
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
}

.theme-elegant page {
  background: linear-gradient(135deg, #f9f0ff 0%, #ede7f6 100%);
}

.theme-ocean page {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.feedback-root {
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 顶部头部 */
.feedback-header {
  height: 200rpx;
  position: relative;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.9) 0%, rgba(115, 209, 61, 0.9) 100%);
  border-radius: 0 0 50rpx 50rpx;
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.feedback-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.feedback-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 筛选器 */
.filter-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: flex;
  gap: 16rpx;
}

.filter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  background: white;
  border: 2rpx solid #e2e8f0;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}

.filter-item.active {
  background: rgba(82, 196, 26, 0.1);
  border-color: #52c41a;
}

.filter-item:active {
  transform: scale(0.98);
}

.filter-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.filter-text {
  font-size: 24rpx;
  color: #555;
  font-weight: 500;
}

.filter-item.active .filter-text {
  color: #52c41a;
}

/* 内容区域 */
.feedback-content {
  padding: 0 32rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: white;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
}

/* 留言列表 */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.feedback-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.feedback-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-type {
  display: flex;
  align-items: center;
}

.type-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.type-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #52c41a;
}

.item-time {
  font-size: 22rpx;
  color: #999;
}

.item-content {
  font-size: 28rpx;
  color: #2d3748;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.item-user, .item-contact {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.user-icon, .contact-icon {
  margin-right: 8rpx;
}

/* 操作区域 */
.action-section {
  margin-top: 48rpx;
  text-align: center;
}

.clear-btn {
  background: linear-gradient(120deg, #ff4d4f 0%, #ff7875 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.clear-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 详情弹窗 */
.detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.detail-popup.show {
  opacity: 1;
  visibility: visible;
}

.detail-popup-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.detail-popup.show .detail-popup-content {
  transform: scale(1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
}

.popup-close:active {
  background: #e8e8e8;
}

/* 详情内容 */
.detail-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.info-label {
  color: #666;
  min-width: 160rpx;
}

.info-value {
  color: #2d3748;
  flex: 1;
}

.content-text {
  font-size: 28rpx;
  color: #2d3748;
  line-height: 1.6;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border-left: 6rpx solid #52c41a;
}

/* 详情操作按钮 */
.detail-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.copy {
  background: #52c41a;
  color: white;
}

.action-btn.delete {
  background: #ff4d4f;
  color: white;
}

.action-btn:active {
  transform: scale(0.98);
}
