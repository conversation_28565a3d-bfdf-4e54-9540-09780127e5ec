/* 账单明细页骨架屏样式 */
.skeleton-container {
  padding: 16px;
  background: #f8fafc;
  min-height: 100vh;
}

.skeleton-search {
  margin-bottom: 16px;
}

.skeleton-search-input {
  width: 100%;
  height: 40px;
  background: #e2e8f0;
  border-radius: 20px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.skeleton-filter-btn {
  width: 60px;
  height: 32px;
  background: #e2e8f0;
  border-radius: 16px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-bill-list {
  background: white;
  border-radius: 12px;
  padding: 16px;
}

.skeleton-bill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-bill-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-circle {
  width: 40px;
  height: 40px;
  background: #e2e8f0;
  border-radius: 50%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-bill-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.skeleton-bill-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.skeleton-text {
  background: #e2e8f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-text-xs { width: 40px; height: 12px; }
.skeleton-text-sm { width: 60px; height: 14px; }
.skeleton-text-md { width: 80px; height: 16px; }
.skeleton-text-lg { width: 60px; height: 18px; }

@keyframes skeleton-loading {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
