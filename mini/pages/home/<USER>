Page({
  data: {
    budget: 0,
    used: 0,
    remain: 0,
    overBudget: false,
    progressPercent: 0,
    outcome: 0,
    lastOutcome: 0,
    income: 0,
    lastIncome: 0,
    categories: [
      { name: '餐饮', icon: 'bill-o' },
      { name: '购物', icon: 'cart-o' },
      { name: '娱乐', icon: 'smile-o' },
      { name: '交通', icon: 'guide-o' },
      { name: '工资', icon: 'balance-o' },
      { name: '理财', icon: 'gold-coin-o' },
      { name: '医疗', icon: 'medal-o' },
      { name: '其他', icon: 'apps-o' }
    ],
    selectedCat: '餐饮',
    bills: [],
    activeTab: 0,
    billList: [],
    showBudgetDialog: false,
    budgetInput: '',
    budgetType: 'month', // 预算类型：month, quarter, year
    budgetTypeLabel: '本月', // 预算类型标签
    currentTheme: 'modern', // 当前主题
    progressColor: 'linear-gradient(90deg,#667eea,#764ba2)', // 进度条颜色
    // 上月账单统计数据
    lastMonthStats: {
      expense: 0,
      income: 0,
      balance: 0,
      count: 0,
      dailyAverage: 0
    },
    lastMonthTopCategories: []
  },
  onLoad() {
    this.loadTheme();
    this.refreshData();
  },
  onShow() {
    this.loadTheme();
    this.refreshData();
    // 同步自定义 tabbar 选中状态
    setTimeout(() => {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 0
        })
      }
    }, 100);
  },
  /**
   * 刷新首页所有数据，全部从本地存储读取
   */
  refreshData() {
    // 获取当前年月
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const ym = `${year}-${month}`;
    // 读取本地账单
    let bills = wx.getStorageSync('bills') || [];
    // 读取预算
    let budget = wx.getStorageSync('budget_' + ym);
    if (budget === '' || budget === undefined) budget = 0;
    // 计算本月收支
    let income = 0, outcome = 0, used = 0;
    bills.forEach(bill => {
      if (bill.date.startsWith(ym)) {
        if (bill.type === '收入') income += Number(bill.amount);
        if (bill.type === '支出') {
          outcome += Number(bill.amount);
          used += Number(bill.amount);
        }
      }
    });
    // 计算上月收支
    let lastYm = `${year}-${(now.getMonth() === 0 ? 12 : now.getMonth()).toString().padStart(2, '0')}`;
    let lastIncome = 0, lastOutcome = 0;
    bills.forEach(bill => {
      if (bill.date.startsWith(lastYm)) {
        if (bill.type === '收入') lastIncome += Number(bill.amount);
        if (bill.type === '支出') lastOutcome += Number(bill.amount);
      }
    });
    // 预算进度
    let percent = 0;
    if (budget > 0) {
      percent = Math.round(used / budget * 100);
      if (percent > 100) percent = 100;
      if (percent < 0) percent = 0;
    }
    const remain = budget - used;
    // 最近账单（所有账单，按时间倒序，最多10条）
    const recentBills = bills.sort((a, b) => {
      // 先按日期倒序，再按ID倒序（确保同一天的账单也有稳定排序）
      const dateCompare = b.date.localeCompare(a.date);
      if (dateCompare !== 0) return dateCompare;
      return (b.id || 0) - (a.id || 0);
    }).slice(0, 10);

    // 计算上月统计数据
    const lastMonthStats = this.calculateLastMonthStats(bills);
    const lastMonthTopCategories = this.getLastMonthTopCategories(bills);

    this.setData({
      budget,
      used,
      remain,
      overBudget: used > budget,
      progressPercent: percent,
      income,
      outcome,
      lastIncome,
      lastOutcome,
      bills: recentBills,
      lastMonthStats,
      lastMonthTopCategories
    });
  },
  selectCat(e) {
    this.setData({ selectedCat: e.currentTarget.dataset.name });
  },
  goRecord() {
    wx.navigateTo({
      url: '/pages/record/index',
    });
  },
  openBudgetDialog() {
    console.log('openBudgetDialog 被触发');
    this.setData({
      showBudgetDialog: true,
      budgetInput: this.data.budget + '',
      budgetType: 'month',
      budgetTypeLabel: '本月'
    });
  },
  onBudgetInputChange(e) {
    this.setData({ budgetInput: e.detail.value });
  },
  onBudgetTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    let label = '';
    switch(type) {
      case 'month': label = '本月'; break;
      case 'quarter': label = '本季度'; break;
      case 'year': label = '本年'; break;
    }
    this.setData({
      budgetType: type,
      budgetTypeLabel: label,
      budgetInput: '' // 切换类型时清空输入
    });
  },
  onBudgetDialogClose() {
    this.setData({ showBudgetDialog: false });
  },

  /**
   * 对话框内容点击事件（阻止冒泡）
   */
  onDialogContentTap() {
    // 空函数，用于阻止事件冒泡到遮罩层
  },
  onBudgetDialogConfirm() {
    const budget = parseFloat(this.data.budgetInput);
    if (!isNaN(budget) && budget > 0) {
      // 保存预算到本地
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      let key = '';

      switch(this.data.budgetType) {
        case 'month':
          key = `budget_${year}-${month.toString().padStart(2, '0')}`;
          break;
        case 'quarter': {
          const quarter = Math.ceil(month / 3);
          key = `budget_quarter_${year}-Q${quarter}`;
          break;
        }
        case 'year':
          key = `budget_year_${year}`;
          break;
      }

      wx.setStorageSync(key, budget);
      this.setData({ showBudgetDialog: false }, this.refreshData);
      wx.showToast({
        title: `已保存${this.data.budgetTypeLabel}预算：￥${budget}`,
        icon: 'success',
        duration: 2000
      });
    } else {
      wx.showToast({ title: '请输入有效金额', icon: 'none' });
    }
  },
  onReady() {},
  onHide() {},
  onUnload() {},
  onPullDownRefresh() {},
  onReachBottom() {},
  onShareAppMessage() {},
  onClickSetting() {
    wx.navigateTo({ url: '/pages/setting/index' });
  },
  onQuickRecord() {
    wx.switchTab({ url: '/pages/record/index' });
  },
  onQuickBill() {
    wx.navigateTo({ url: '/pages/bill-detail/index' });
  },
  onQuickStat() {
    wx.navigateTo({ url: '/pages/stat/index' });
  },
  onQuickMine() {
    wx.switchTab({ url: '/pages/mine/index' });
  },
  onBillDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/bill-detail/index?id=${id}` });
  },

  /**
   * 加载主题
   */
  loadTheme() {
    const app = getApp();
    const currentTheme = app.globalData.currentTheme;
    this.setData({
      currentTheme,
      progressColor: this.getProgressColor(currentTheme)
    });
  },

  /**
   * 主题变更回调
   */
  onThemeChange(themeId) {
    this.setData({
      currentTheme: themeId,
      progressColor: this.getProgressColor(themeId)
    });
  },

  /**
   * 获取进度条颜色
   */
  getProgressColor(themeId) {
    const colorMap = {
      'modern': 'linear-gradient(90deg,#667eea,#764ba2)',
      'warm': 'linear-gradient(90deg,#ff9a9e,#fecfef)',
      'nature': 'linear-gradient(90deg,#a8edea,#fed6e3)',
      'elegant': 'linear-gradient(90deg,#d299c2,#fef9d7)',
      'ocean': 'linear-gradient(90deg,#89f7fe,#66a6ff)'
    };
    return colorMap[themeId] || colorMap.modern;
  },

  /**
   * 计算上月统计数据
   */
  calculateLastMonthStats(bills) {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // 筛选上月账单
    const lastMonthBills = bills.filter(bill => {
      const billDate = new Date(bill.date);
      return billDate >= lastMonth && billDate <= lastMonthEnd;
    });

    // 计算收支
    const expenseBills = lastMonthBills.filter(bill => bill.type === '支出');
    const incomeBills = lastMonthBills.filter(bill => bill.type === '收入');

    const totalExpense = expenseBills.reduce((sum, bill) => sum + bill.amount, 0);
    const totalIncome = incomeBills.reduce((sum, bill) => sum + bill.amount, 0);
    const balance = totalIncome - totalExpense;

    // 计算日均支出（按上月实际天数）
    const daysInLastMonth = lastMonthEnd.getDate();
    const dailyAverage = Math.round(totalExpense / daysInLastMonth);

    return {
      expense: totalExpense,
      income: totalIncome,
      balance: balance,
      count: lastMonthBills.length,
      dailyAverage: dailyAverage
    };
  },

  /**
   * 获取上月主要支出分类
   */
  getLastMonthTopCategories(bills) {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // 筛选上月支出账单
    const lastMonthExpenses = bills.filter(bill => {
      const billDate = new Date(bill.date);
      return billDate >= lastMonth && billDate <= lastMonthEnd && bill.type === '支出';
    });

    // 按分类统计
    const categoryStats = {};
    lastMonthExpenses.forEach(bill => {
      if (categoryStats[bill.category]) {
        categoryStats[bill.category] += bill.amount;
      } else {
        categoryStats[bill.category] = bill.amount;
      }
    });

    // 获取前3个分类
    return Object.entries(categoryStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([name, amount]) => ({ name, amount }));
  },

  /**
   * 查看上月详情
   */
  onViewLastMonthDetails() {
    const stats = this.data.lastMonthStats;
    const categories = this.data.lastMonthTopCategories;

    let categoryText = '';
    if (categories.length > 0) {
      categoryText = categories.map(cat => `• ${cat.name}：￥${cat.amount}`).join('\n');
    } else {
      categoryText = '暂无支出记录';
    }

    wx.showModal({
      title: '上月账单详情',
      content: `统计周期：上个月\n\n总收入：￥${stats.income}\n总支出：￥${stats.expense}\n结余：￥${stats.balance}\n账单数量：${stats.count}笔\n日均支出：￥${stats.dailyAverage}\n\n主要支出分类：\n${categoryText}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看完整账单',
      success: (res) => {
        if (res.confirm) {
          // 跳转到账单页面
          wx.navigateTo({
            url: '/pages/bill-detail/index'
          });
        }
      }
    });
  }
});
