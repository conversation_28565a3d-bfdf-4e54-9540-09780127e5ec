page {
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* 主题背景 */
.theme-modern page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.theme-warm page {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.theme-nature page {
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
}

.theme-elegant page {
  background: linear-gradient(135deg, #f9f0ff 0%, #ede7f6 100%);
}

.theme-ocean page {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}
.home-title {
  font-size: 22px;
  color: #2d3748;
  font-weight: 700;
  letter-spacing: 1px;
  margin: 28px 0 0 24px;
}
.home-bg {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #e8edf5 0%, #dbe6f6 100%);
  padding-bottom: 100px;
}

.budget-card {
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  padding: 20px 18px;
  margin: 28px 18px 0 18px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  position: relative;
}
.budget-header {
  font-size: 15px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}
.budget-title { margin-left: 4px; }
.budget-over {
  color: #e53e3e;
  font-size: 13px;
  font-weight: 600;
  margin-left: 8px;
}
.budget-amount {
  font-size: 22px;
  font-weight: 700;
  color: #667eea;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.budget-progress-row {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 8px 0 8px 0;
  min-width: 0;
}
.budget-progress-wrap {
  flex: 1;
  margin: 0 8px;
}
.budget-used, .budget-remain {
  font-size: 13px;
  color: #718096;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.budget-progress {
  flex: 1;
}
.budget-percent {
  font-size: 14px;
  color: #fff;
  background: #e53e3e;
  border-radius: 12px;
  padding: 2px 10px;
  margin: 0 4px;
}
.btn-budget {
  width: 90px !important;
  height: 30px !important;
  border-radius: 18px !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #fff !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 4px 10px !important;
  letter-spacing: 1px !important;
  transition: all 0.3s ease !important;
}

/* 主题预算按钮样式 */
.theme-modern .btn-budget {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 16px rgba(102,126,234,0.3) !important;
}

.theme-warm .btn-budget {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%) !important;
  box-shadow: 0 4px 16px rgba(255,154,158,0.3) !important;
}

.theme-nature .btn-budget {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%) !important;
  box-shadow: 0 4px 16px rgba(168,237,234,0.3) !important;
}

.theme-elegant .btn-budget {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%) !important;
  box-shadow: 0 4px 16px rgba(210,153,194,0.3) !important;
}

.theme-ocean .btn-budget {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%) !important;
  box-shadow: 0 4px 16px rgba(102,166,255,0.3) !important;
}


.summary-row {
  margin: 28px 18px 0 18px;
  display: flex;
  gap: 16px;
}
.summary-card {
  flex: 1;
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  padding: 18px 14px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  border: 1px solid rgba(255,255,255,0.2);
  min-width: 0;
}
.summary-label {
  color: #718096;
  font-size: 14px;
  font-weight: 500;
}
.summary-value {
  font-size: 22px;
  font-weight: 700;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.summary-card.out .summary-value { color: #e53e3e; }
.summary-card.in .summary-value { color: #38a169; }
.summary-compare {
  font-size: 13px;
  color: #718096;
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
  overflow: hidden;
}
.compare-value { font-weight: 600; }
.arrow-icon {
  width: 18px;
  height: 18px;
}

/* 上月账单统计区 */
.last-month-section {
  margin: 28rpx 36rpx 0 36rpx;
}

.last-month-card {
  background: rgba(255,255,255,0.95);
  border-radius: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0,0,0,0.08);
  padding: 40rpx 36rpx;
  border: 2rpx solid rgba(255,255,255,0.2);
}

.last-month-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.last-month-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.last-month-detail-btn {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-weight: 500;
}

.last-month-overview {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.overview-item {
  flex: 1;
  border-radius: 24rpx;
  padding: 24rpx;
  border: 2rpx solid;
}

.overview-item.expense {
  background: rgba(229,62,62,0.05);
  border-color: rgba(229,62,62,0.1);
}

.overview-item.income {
  background: rgba(56,161,105,0.05);
  border-color: rgba(56,161,105,0.1);
}

.overview-item.balance {
  background: rgba(102,126,234,0.05);
  border-color: rgba(102,126,234,0.1);
}

.overview-label {
  display: block;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.overview-item.expense .overview-label {
  color: #e53e3e;
}

.overview-item.income .overview-label {
  color: #38a169;
}

.overview-item.balance .overview-label {
  color: #667eea;
}

.overview-value {
  font-size: 36rpx;
  font-weight: 700;
}

.overview-item.expense .overview-value {
  color: #e53e3e;
}

.overview-item.income .overview-value {
  color: #38a169;
}

.last-month-categories {
  margin-bottom: 32rpx;
}

.categories-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
  display: block;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.category-item {
  background: rgba(102,126,234,0.05);
  border-radius: 16rpx;
  padding: 12rpx 20rpx;
  border: 2rpx solid rgba(102,126,234,0.1);
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.category-name {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

.category-amount {
  font-size: 24rpx;
  color: #718096;
}

.last-month-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 2rpx solid rgba(0,0,0,0.05);
}

.stat-item {
  font-size: 26rpx;
  color: #718096;
}

.bill-section {
  margin: 28px 18px 0 18px;
}
.bill-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}
.bill-list {}
.bill-item {
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  padding: 14px 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid rgba(255,255,255,0.2);
}
.bill-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.bill-cat {
  font-size: 15px;
  font-weight: 600;
  color: #2d3748;
}
.bill-type.in { color: #38a169; }
.bill-type.out { color: #e53e3e; }
.bill-date {
  font-size: 13px;
  color: #888;
}
.bill-amount {
  font-weight: 700;
  font-size: 18px;
}
.bill-amount.in { color: #38a169; }
.bill-amount.out { color: #e53e3e; }

/* 空状态样式 */
.bill-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

.empty-title {
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #718096;
  margin-bottom: 24px;
  line-height: 1.4;
}

.empty-action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.fab {
  position: fixed;
  right: 36px;
  bottom: 110px;
  width: 60px;
  height: 60px;
  min-width: 60px;
  min-height: 60px;
  max-width: 60px;
  max-height: 60px;
  border-radius: 50%;
  z-index: 2000;
  color: #fff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 60px;
  font-size: 28px;
  box-sizing: border-box;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 主题悬浮按钮样式 */
.theme-modern .fab {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 16px rgba(102,126,234,0.3);
}

.theme-warm .fab {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
  box-shadow: 0 4px 16px rgba(255,154,158,0.3);
}

.theme-nature .fab {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4px 16px rgba(168,237,234,0.3);
}

.theme-elegant .fab {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%);
  box-shadow: 0 4px 16px rgba(210,153,194,0.3);
}

.theme-ocean .fab {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%);
  box-shadow: 0 4px 16px rgba(102,166,255,0.3);
}
.fab .van-icon {
  font-size: 28px !important;
  color: #fff !important;
  line-height: 1 !important;
  display: block !important;
}

.tab-bar {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  width: 100%;
  border-radius: 0;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.10);
  border-top: 1px solid #e0e0e0;
  z-index: 1000;
  display: flex;
}
.tab-item {
  flex: 1;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #b0b6c3;
  line-height: 20px;
  padding: 6px 0 4px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.tab-item.active {
  color: #667eea;
  font-weight: 700;
}
.tab-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
}

.card-list {
  display: flex;
  justify-content: space-between;
  margin: 36px 20px 0 20px;
  gap: 20px;
}
.card-item {
  flex: 1;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  padding: 24px 0 18px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.card-item.out .card-value {
  color: #ff5a5f;
}
.card-item.in .card-value {
  color: #2ecc71;
}
.card-title {
  font-size: 16px;
  color: #888;
}
.card-value {
  font-size: 26px;
  font-weight: 600;
  margin-top: 10px;
}

::-webkit-scrollbar { display: none; }
.status-bar {
  height: 44px;
  background: transparent;
}
.title-bar {
  height: 48px;
  line-height: 48px;
  font-size: 22px;
  font-weight: 600;
  text-align: center;
  background: transparent;
  color: #222;
}
.main-card {
  margin: 32px 20px 0 20px;
  border-radius: 32px;
  background: #fff;
  box-shadow: 0 8px 32px rgba(0,0,0,0.10);
  padding: 36px 24px 28px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.balance-label {
  font-size: 20px;
  color: #888;
  text-align: center;
  margin-bottom: 8px;
}
.balance-amount {
  font-size: 38px;
  font-weight: bold;
  color: #2a8cff;
  text-align: center;
  margin-bottom: 24px;
}
.quick-action {
  display: flex;
  justify-content: center;
  gap: 24px;
  width: 100%;
}
.btn-record, .btn-budget {
  flex: 1;
  color: #fff;
  border: none;
  border-radius: 32px;
  font-size: 18px;
  padding: 4px 0;
  transition: all 0.3s ease;
}

/* 主题按钮样式 */
.theme-modern .btn-record, .theme-modern .btn-budget {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(102,126,234,0.3);
}

.theme-warm .btn-record, .theme-warm .btn-budget {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
  box-shadow: 0 2px 8px rgba(255,154,158,0.3);
}

.theme-nature .btn-record, .theme-nature .btn-budget {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 2px 8px rgba(168,237,234,0.3);
}

.theme-elegant .btn-record, .theme-elegant .btn-budget {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%);
  box-shadow: 0 2px 8px rgba(210,153,194,0.3);
}

.theme-ocean .btn-record, .theme-ocean .btn-budget {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%);
  box-shadow: 0 2px 8px rgba(102,166,255,0.3);
}

.icon {
  width: 48px;
  height: 48px;
  display: block;
}

/* 悬浮按钮固定底部样式 */
.fab-wrap { position: fixed; right: 32rpx; bottom: 120rpx; z-index: 99; }

.page-root {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 120rpx; /* 预留tabbar高度，避免内容被tabbar遮挡 */
}

/* 预算对话框样式 */
.budget-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.budget-dialog-mask.show {
  opacity: 1;
  visibility: visible;
}

.budget-dialog-content {
  width: 80%;
  max-width: 400px;
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.budget-dialog-mask.show .budget-dialog-content {
  transform: scale(1);
}

.budget-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.budget-dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.budget-dialog-close {
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 4px;
}

.budget-tabs {
  display: flex;
  margin-bottom: 20px;
  border-radius: 8px;
  background: #f5f5f5;
  padding: 4px;
}

.budget-tab {
  flex: 1;
  text-align: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

.budget-tab.active {
  background: #667eea;
  color: #fff;
  font-weight: 600;
}

.budget-input-section {
  margin-bottom: 20px;
}

.budget-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.budget-input {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  line-height: 48px;
}

.budget-save-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  border: none;
  border-radius: 22px;
  transition: all 0.3s ease;
}

/* 主题预算保存按钮样式 */
.theme-modern .budget-save-btn {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.theme-warm .budget-save-btn {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
}

.theme-nature .budget-save-btn {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
}

.theme-elegant .budget-save-btn {
  background: linear-gradient(90deg, #d299c2 0%, #fef9d7 100%);
}

.theme-ocean .budget-save-btn {
  background: linear-gradient(90deg, #89f7fe 0%, #66a6ff 100%);
}
