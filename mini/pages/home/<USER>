/* 首页骨架屏样式 */
.skeleton-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.skeleton-title {
  width: 120px;
  height: 28px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 20px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.skeleton-text {
  background: #e2e8f0;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-text-xs { width: 60px; height: 12px; }
.skeleton-text-sm { width: 80px; height: 14px; }
.skeleton-text-md { width: 120px; height: 16px; }
.skeleton-text-lg { width: 100px; height: 20px; }

.skeleton-progress {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin: 12px 0;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.skeleton-stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-bill-list {
  margin-top: 16px;
}

.skeleton-bill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-bill-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-circle {
  width: 40px;
  height: 40px;
  background: #e2e8f0;
  border-radius: 50%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-bill-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

@keyframes skeleton-loading {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
