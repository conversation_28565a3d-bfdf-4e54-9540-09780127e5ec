<view class="page-root theme-{{currentTheme}}">
  <!-- 顶部大标题 -->
  <view class="home-title">简易记账</view>

  <!-- 预算卡片 -->
  <view class="budget-card">
    <view class="budget-header">
      <van-icon name="balance-o" size="18" color="#667eea" />
      <text class="budget-title">本月预算</text>
      <text wx:if="{{overBudget}}" class="budget-over">已超额</text>
    </view>
    <view class="budget-amount">￥{{budget}}</view>
    <view class="budget-progress-row">
      <text class="budget-used">已用￥{{used}}</text>
      <view class="budget-progress-wrap">
        <van-progress percentage="{{progressPercent}}" color="{{overBudget ? '#e53e3e' : progressColor}}" stroke-width="8" />
      </view>
      <text class="budget-remain">剩余￥{{remain > 0 ? remain : 0}}</text>
    </view>
    <button class="btn-budget" bind:tap="openBudgetDialog">设置预算</button>
  </view>

  <!-- 支出/收入统计卡片区 -->
  <view class="summary-row">
    <view class="summary-card out">
      <text class="summary-label">本月支出</text>
      <text class="summary-value">-￥{{outcome}}</text>
      <view class="summary-compare">
        上月: ￥{{lastOutcome}}
        <van-icon name="arrow-up" color="#e53e3e" wx:if="{{outcome > lastOutcome}}" />
        <van-icon name="arrow-down" color="#38a169" wx:if="{{outcome <= lastOutcome}}" />
        <text class="compare-value" style="color:{{outcome > lastOutcome ? '#e53e3e' : '#38a169'}}">{{Math.abs(outcome-lastOutcome)}}</text>
      </view>
    </view>
    <view class="summary-card in">
      <text class="summary-label">本月收入</text>
      <text class="summary-value">+￥{{income}}</text>
      <view class="summary-compare">
        上月: ￥{{lastIncome}}
        <van-icon name="arrow-up" color="#38a169" wx:if="{{income > lastIncome}}" />
        <van-icon name="arrow-down" color="#e53e3e" wx:if="{{income <= lastIncome}}" />
        <text class="compare-value" style="color:{{income > lastIncome ? '#38a169' : '#e53e3e'}}">{{Math.abs(income-lastIncome)}}</text>
      </view>
    </view>
  </view>

  <!-- 上月账单统计区 -->
  <view class="last-month-section">
    <view class="last-month-card">
      <view class="last-month-header">
        <view class="last-month-title">
          <van-icon name="calendar-o" color="#667eea" size="18px" />
          <text>上月账单统计</text>
        </view>
        <view class="last-month-detail-btn" bind:tap="onViewLastMonthDetails">查看详情</view>
      </view>

      <!-- 上月收支概览 -->
      <view class="last-month-overview">
        <view class="overview-item expense">
          <text class="overview-label">支出</text>
          <text class="overview-value">￥{{lastMonthStats.expense}}</text>
        </view>
        <view class="overview-item income">
          <text class="overview-label">收入</text>
          <text class="overview-value">￥{{lastMonthStats.income}}</text>
        </view>
        <view class="overview-item balance">
          <text class="overview-label">结余</text>
          <text class="overview-value" style="color:{{lastMonthStats.balance >= 0 ? '#38a169' : '#e53e3e'}}">￥{{lastMonthStats.balance}}</text>
        </view>
      </view>

      <!-- 上月分类统计 -->
      <view class="last-month-categories">
        <text class="categories-title">主要支出分类</text>
        <view class="categories-list">
          <view class="category-item" wx:for="{{lastMonthTopCategories}}" wx:key="name">
            <text class="category-name">{{item.name}}</text>
            <text class="category-amount">￥{{item.amount}}</text>
          </view>
        </view>
      </view>

      <!-- 上月统计信息 -->
      <view class="last-month-stats">
        <text class="stat-item">账单数量：{{lastMonthStats.count}}笔</text>
        <text class="stat-item">日均支出：￥{{lastMonthStats.dailyAverage}}</text>
      </view>
    </view>
  </view>

  <!-- 最近账单区 -->
  <view class="bill-section">
    <view class="bill-title">最近账单</view>

    <!-- 有账单数据时显示列表 -->
    <view wx:if="{{bills.length > 0}}" class="bill-list">
      <view class="bill-item" wx:for="{{bills}}" wx:key="id" data-id="{{item.id}}" bind:click="onBillDetail">
        <view class="bill-info">
          <text class="bill-cat">{{item.category}}</text>
          <text class="bill-date">{{item.date}}<text wx:if="{{item.remark}}"> · {{item.remark}}</text></text>
        </view>
        <text class="bill-amount {{item.type === '收入' ? 'in' : 'out'}}">{{item.type === '收入' ? '+' : '-'}}￥{{item.amount}}</text>
      </view>
    </view>

    <!-- 无账单数据时显示空状态 -->
    <view wx:else class="bill-empty">
      <view class="empty-icon">📝</view>
      <view class="empty-title">还没有账单记录</view>
      <view class="empty-desc">点击右下角按钮开始记账吧</view>

    </view>
  </view>

  <!-- 悬浮记一笔按钮（用view+van-icon，彻底避免button原生样式干扰） -->
  <view class="fab" bindtap="goRecord">
    <van-icon name="edit" />
  </view>
</view>

<!-- 使用原生tabBar，无需在页面中定义 -->

<!-- 预算设置对话框 -->
<view class="budget-dialog-mask {{showBudgetDialog ? 'show' : ''}}" bindtap="onBudgetDialogClose">
  <view class="budget-dialog-content" catchtap="onDialogContentTap">
    <view class="budget-dialog-header">
      <view class="budget-dialog-title">设置预算</view>
      <text class="budget-dialog-close" bindtap="onBudgetDialogClose">✕</text>
    </view>

    <!-- 预算类型选择 -->
    <view class="budget-tabs">
      <view
        class="budget-tab {{budgetType === 'month' ? 'active' : ''}}"
        data-type="month"
        bind:tap="onBudgetTypeChange"
      >月预算</view>
      <view
        class="budget-tab {{budgetType === 'quarter' ? 'active' : ''}}"
        data-type="quarter"
        bind:tap="onBudgetTypeChange"
      >季度预算</view>
      <view
        class="budget-tab {{budgetType === 'year' ? 'active' : ''}}"
        data-type="year"
        bind:tap="onBudgetTypeChange"
      >年预算</view>
    </view>

    <!-- 预算输入 -->
    <view class="budget-input-section">
      <text class="budget-label">{{budgetTypeLabel}}预算</text>
      <input
        class="budget-input"
        value="{{budgetInput}}"
        type="number"
        placeholder="请输入{{budgetTypeLabel}}预算金额"
        bind:input="onBudgetInputChange"
      />
    </view>

    <!-- 保存按钮 -->
    <button
      class="budget-save-btn"
      bind:tap="onBudgetDialogConfirm"
    >保存</button>
  </view>
</view>

