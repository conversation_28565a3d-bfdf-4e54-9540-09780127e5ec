/**app.wxss**/
@import "styles/theme.wxss";

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 全局容器 */
.container {
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 文本样式 */
.text-primary {
  transition: color 0.3s ease;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 间距样式 */
.mt-16 { margin-top: 16px; }
.mb-16 { margin-bottom: 16px; }
.ml-16 { margin-left: 16px; }
.mr-16 { margin-right: 16px; }

.pt-16 { padding-top: 16px; }
.pb-16 { padding-bottom: 16px; }
.pl-16 { padding-left: 16px; }
.pr-16 { padding-right: 16px; }
