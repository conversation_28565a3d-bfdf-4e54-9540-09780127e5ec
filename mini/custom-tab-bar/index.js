Component({
  data: {
    selected: 0,
    color: "#718096",
    selectedColor: "#667eea",
    currentTheme: 'modern',
    list: [
      {
        pagePath: "/pages/home/<USER>",
        iconPath: "home-o",
        selectedIconPath: "home-o",
        text: "首页"
      },
      {
        pagePath: "/pages/bill-detail/index",
        iconPath: "notes-o",
        selectedIconPath: "notes-o",
        text: "账单"
      },
      {
        pagePath: "/pages/stat/index",
        iconPath: "chart-trending-o",
        selectedIconPath: "chart-trending-o",
        text: "统计"
      },
      {
        pagePath: "/pages/mine/index",
        iconPath: "user-o",
        selectedIconPath: "user-o",
        text: "我的"
      }
    ]
  },
  attached() {
    // 初始化时设置首页为选中状态
    this.setData({
      selected: 0
    });
  },
  lifetimes: {
    attached() {
      this.loadTheme();
    }
  },

  methods: {
    switchTab(e) {
      // Vant tabbar 的 change 事件直接传递索引值
      const index = e.detail;

      // 检查索引是否有效
      if (typeof index === 'number' && index >= 0 && index < this.data.list.length) {
        const url = this.data.list[index].pagePath

        wx.switchTab({
          url,
          success: () => {
            this.setData({
              selected: index
            })
          }
        })
      }
    },

    /**
     * 加载主题
     */
    loadTheme() {
      const app = getApp();
      if (app && app.globalData) {
        const currentTheme = app.globalData.currentTheme;
        const selectedColor = this.getThemeColor(currentTheme);
        this.setData({
          currentTheme,
          selectedColor
        });
      }
    },

    /**
     * 获取主题颜色
     */
    getThemeColor(themeId) {
      const colorMap = {
        'modern': '#667eea',
        'warm': '#ff9a9e',
        'nature': '#a8edea',
        'elegant': '#d299c2',
        'ocean': '#66a6ff'
      };
      return colorMap[themeId] || colorMap.modern;
    },

    /**
     * 主题变更回调
     */
    onThemeChange(themeId) {
      const selectedColor = this.getThemeColor(themeId);
      this.setData({
        currentTheme: themeId,
        selectedColor
      });
    }
  }
})
