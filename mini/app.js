// app.js
const { storage, billManager, budgetManager, userManager } = require('./utils/storage');

App({
  globalData: {
    // 全局数据管理器
    storage,
    billManager,
    budgetManager,
    userManager,
    
    // 应用配置
    appConfig: {
      version: '1.0.0',
      name: '简易记账',
      theme: 'default'
    },

    // 用户信息
    userInfo: null,

    // 系统信息
    systemInfo: null,

    // 主题管理
    currentTheme: 'modern',
    themes: {
      modern: {
        id: 'modern',
        name: '现代科技',
        primary: '#667eea',
        secondary: '#764ba2',
        gradient: 'linear-gradient(120deg, #667eea 0%, #764ba2 100%)',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
      },
      warm: {
        id: 'warm',
        name: '温暖橙光',
        primary: '#ff9a9e',
        secondary: '#fecfef',
        gradient: 'linear-gradient(120deg, #ff9a9e 0%, #fecfef 100%)',
        background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
      },
      nature: {
        id: 'nature',
        name: '自然绿意',
        primary: '#a8edea',
        secondary: '#fed6e3',
        gradient: 'linear-gradient(120deg, #a8edea 0%, #fed6e3 100%)',
        background: 'linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%)'
      },
      elegant: {
        id: 'elegant',
        name: '优雅紫调',
        primary: '#d299c2',
        secondary: '#fef9d7',
        gradient: 'linear-gradient(120deg, #d299c2 0%, #fef9d7 100%)',
        background: 'linear-gradient(135deg, #f9f0ff 0%, #ede7f6 100%)'
      },
      ocean: {
        id: 'ocean',
        name: '海洋蓝调',
        primary: '#89f7fe',
        secondary: '#66a6ff',
        gradient: 'linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%)',
        background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)'
      }
    }
  },

  onLaunch() {
    console.log('应用启动');

    // 获取系统信息
    this.getSystemInfo();

    // 初始化主题
    this.initTheme();

    // 初始化用户信息
    this.initUserInfo();

    // 检查数据完整性
    this.checkDataIntegrity();

    // 初始化默认数据
    this.initDefaultData();
  },

  onShow() {
    console.log('应用显示');
  },

  onHide() {
    console.log('应用隐藏');
  },

  onError(error) {
    console.error('应用错误:', error);
    
    // 错误上报（实际项目中可以上报到服务器）
    this.reportError(error);
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 初始化用户信息
   */
  initUserInfo() {
    try {
      const userInfo = this.globalData.userManager.getUserInfo();
      this.globalData.userInfo = userInfo;
      console.log('用户信息:', userInfo);
    } catch (error) {
      console.error('初始化用户信息失败:', error);
    }
  },

  /**
   * 检查数据完整性
   */
  checkDataIntegrity() {
    try {
      const storageInfo = this.globalData.storage.getStorageInfo();
      console.log('存储信息:', storageInfo);
      
      // 检查关键数据是否存在
      const bills = this.globalData.billManager.getBills();
      console.log(`账单数据: ${bills.length} 条记录`);
      
      const budgets = this.globalData.budgetManager.getAllBudgets();
      console.log(`预算数据: ${budgets.length} 条记录`);
      
    } catch (error) {
      console.error('数据完整性检查失败:', error);
    }
  },

  /**
   * 初始化默认数据
   */
  initDefaultData() {
    try {
      // 新用户不需要默认数据，保持空白状态
      console.log('新用户初始化完成，无默认数据');
    } catch (error) {
      console.error('初始化默认数据失败:', error);
    }
  },

  /**
   * 格式化日期
   * @param {Date} date 日期对象
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 错误上报
   * @param {Error} error 错误对象
   */
  reportError(error) {
    // 实际项目中可以上报到服务器
    console.log('错误上报:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: this.globalData.systemInfo?.system || 'unknown'
    });
  },

  /**
   * 获取全局数据管理器
   */
  getDataManager() {
    return {
      storage: this.globalData.storage,
      billManager: this.globalData.billManager,
      budgetManager: this.globalData.budgetManager,
      userManager: this.globalData.userManager
    };
  },

  /**
   * 更新用户信息
   * @param {object} userInfo 用户信息
   */
  updateUserInfo(userInfo) {
    this.globalData.userInfo = { ...this.globalData.userInfo, ...userInfo };
    return this.globalData.userManager.updateUserInfo(userInfo);
  },

  /**
   * 获取应用配置
   */
  getAppConfig() {
    return this.globalData.appConfig;
  },

  /**
   * 获取系统信息
   */
  getSystemInfoData() {
    return this.globalData.systemInfo;
  },

  /**
   * 初始化主题
   */
  initTheme() {
    const savedTheme = wx.getStorageSync('currentTheme') || 'modern';
    this.globalData.currentTheme = savedTheme;
  },

  /**
   * 设置主题
   */
  setTheme(themeId) {
    if (this.globalData.themes[themeId]) {
      this.globalData.currentTheme = themeId;
      wx.setStorageSync('currentTheme', themeId);

      // 通知所有页面更新主题
      this.notifyThemeChange(themeId);
    }
  },

  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return this.globalData.themes[this.globalData.currentTheme] || this.globalData.themes.modern;
  },

  /**
   * 通知所有页面主题变更
   */
  notifyThemeChange(themeId) {
    // 获取当前页面栈
    const pages = getCurrentPages();

    // 通知所有页面更新主题
    pages.forEach(page => {
      if (page.onThemeChange && typeof page.onThemeChange === 'function') {
        page.onThemeChange(themeId);
      }
    });
  }
});
