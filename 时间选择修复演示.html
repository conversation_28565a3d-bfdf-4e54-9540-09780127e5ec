<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>时间选择功能修复演示</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
    
    .demo-container {
      max-width: 375px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .demo-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      text-align: center;
    }
    
    .demo-content {
      padding: 20px;
    }
    
    /* 键盘样式 */
    .custom-keyboard {
      background: #f7f7fa;
      border-radius: 18px;
      padding: 16px;
      margin: 20px 0;
    }
    
    .keyboard-row {
      display: flex;
      width: 100%;
      margin-bottom: 8px;
    }
    
    .key {
      flex: 1;
      margin: 0 6px;
      height: 48px;
      background: #fff;
      border: none;
      border-radius: 12px;
      font-size: 20px;
      color: #222;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      transition: background 0.15s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      cursor: pointer;
    }
    
    .key:active {
      background: #f0e9d2;
    }
    
    .key-today {
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
      color: white !important;
      flex-direction: column;
      font-size: 12px;
      gap: 2px;
      font-weight: 500;
    }
    
    .key-today:active {
      background: linear-gradient(90deg, #5a6fd8 0%, #6a4190 100%) !important;
    }
    
    .today-key .icon {
      font-size: 16px;
    }
    
    /* 修复后的弹窗样式 */
    .datetime-popup {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.5);
      display: none;
      align-items: flex-end;
      z-index: 1000;
    }
    
    .datetime-popup.show {
      display: flex;
    }
    
    .datetime-content {
      background: white;
      border-radius: 20px 20px 0 0;
      padding: 20px;
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
      box-sizing: border-box;
      max-height: 60vh;
      overflow-y: auto;
    }
    
    .popup-header {
      text-align: center;
      margin-bottom: 20px;
      width: 100%;
    }
    
    .popup-title {
      font-size: 18px;
      font-weight: 600;
      color: #2d3748;
      margin: 0;
    }
    
    .picker-section {
      margin: 20px 0;
      width: 100%;
      box-sizing: border-box;
    }
    
    .picker-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
      display: block;
    }
    
    .picker-display {
      padding: 15px;
      background: #f5f5f5;
      border-radius: 8px;
      text-align: center;
      font-size: 16px;
      color: #333;
      border: 1px solid #e0e0e0;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 100%;
      box-sizing: border-box;
      display: block;
    }
    
    .picker-display:hover {
      background: #e8e8e8;
      border-color: #667eea;
    }
    
    .popup-actions {
      display: flex;
      gap: 12px;
      margin-top: 20px;
      width: 100%;
      box-sizing: border-box;
    }
    
    .popup-btn {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .popup-btn.cancel {
      background: #f5f5f5;
      color: #666;
    }
    
    .popup-btn.confirm {
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .popup-btn:hover {
      transform: translateY(-1px);
    }
    
    /* 当前时间显示 */
    .current-datetime {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      margin: 20px 0;
      text-align: center;
    }
    
    .current-datetime h3 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-size: 16px;
    }
    
    .datetime-display {
      font-size: 24px;
      font-weight: bold;
      color: #667eea;
      margin: 8px 0;
    }
    
    .datetime-note {
      font-size: 14px;
      color: #666;
      margin-top: 8px;
    }
    
    /* 修复说明 */
    .fix-list {
      background: #e8f5e8;
      border-radius: 12px;
      padding: 16px;
      margin-top: 20px;
      border-left: 4px solid #38a169;
    }
    
    .fix-list h3 {
      margin: 0 0 12px 0;
      color: #2d3748;
      font-size: 16px;
    }
    
    .fix-list ul {
      margin: 0;
      padding-left: 20px;
      color: #4a5568;
      line-height: 1.6;
    }
    
    .fix-list li {
      margin-bottom: 8px;
    }
    
    .feature-list {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      margin-top: 20px;
    }
    
    .feature-list h3 {
      margin: 0 0 12px 0;
      color: #2d3748;
      font-size: 16px;
    }
    
    .feature-list ul {
      margin: 0;
      padding-left: 20px;
      color: #4a5568;
      line-height: 1.6;
    }
    
    .feature-list li {
      margin-bottom: 8px;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <div class="demo-header">
      <h1 style="margin: 0; font-size: 20px;">时间选择功能修复</h1>
      <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">修复弹窗滑动问题</p>
    </div>
    
    <div class="demo-content">
      <!-- 当前选择的日期时间 -->
      <div class="current-datetime">
        <h3>当前选择时间</h3>
        <div class="datetime-display" id="currentDateTime">今天 14:30</div>
        <div class="datetime-note">点击下方"今天"按钮可修改时间</div>
      </div>
      
      <!-- 模拟键盘 -->
      <div class="custom-keyboard">
        <div class="keyboard-row">
          <div class="key">7</div>
          <div class="key">8</div>
          <div class="key">9</div>
          <div class="key key-today" onclick="showDateTimePicker()">
            <span class="icon">📅</span>
            <span>今天</span>
          </div>
        </div>
        <div class="keyboard-row">
          <div class="key">4</div>
          <div class="key">5</div>
          <div class="key">6</div>
          <div class="key">-</div>
        </div>
        <div class="keyboard-row">
          <div class="key">1</div>
          <div class="key">2</div>
          <div class="key">3</div>
          <div class="key">+</div>
        </div>
        <div class="keyboard-row">
          <div class="key">.</div>
          <div class="key">0</div>
          <div class="key">⌫</div>
        </div>
      </div>
      
      <!-- 修复说明 -->
      <div class="fix-list">
        <h3>✅ 已修复的问题</h3>
        <ul>
          <li><strong>移除Toast提示</strong> - 去除了调试用的Toast弹窗</li>
          <li><strong>防止水平滑动</strong> - 添加overflow-x: hidden限制</li>
          <li><strong>宽度限制</strong> - 确保弹窗内容不超出屏幕宽度</li>
          <li><strong>样式优化</strong> - 改进picker组件的显示样式</li>
          <li><strong>事件优化</strong> - 使用catchtap防止事件冒泡</li>
          <li><strong>清理代码</strong> - 移除调试日志，优化代码结构</li>
        </ul>
      </div>
      
      <div class="feature-list">
        <h3>🎯 修复后的效果</h3>
        <ul>
          <li>点击"今天"按钮直接打开日期时间选择器</li>
          <li>弹窗固定在底部，不会左右滑动</li>
          <li>内容完全适配屏幕宽度</li>
          <li>选择器操作流畅，无滚动问题</li>
          <li>按钮样式美观，交互反馈良好</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 修复后的日期时间选择器弹窗 -->
  <div class="datetime-popup" id="datetimePopup">
    <div class="datetime-content">
      <div class="popup-header">
        <div class="popup-title">选择日期时间</div>
      </div>
      
      <!-- 日期选择 -->
      <div class="picker-section">
        <div class="picker-label">日期</div>
        <input type="date" id="dateInput" class="picker-display" />
      </div>
      
      <!-- 时间选择 -->
      <div class="picker-section">
        <div class="picker-label">时间</div>
        <input type="time" id="timeInput" class="picker-display" />
      </div>
      
      <div class="popup-actions">
        <button class="popup-btn cancel" onclick="hideDateTimePicker()">取消</button>
        <button class="popup-btn confirm" onclick="confirmDateTime()">确定</button>
      </div>
    </div>
  </div>

  <script>
    // 初始化当前时间
    function initCurrentTime() {
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const timeStr = now.toTimeString().slice(0, 5);
      
      document.getElementById('dateInput').value = dateStr;
      document.getElementById('timeInput').value = timeStr;
      
      updateDisplay(dateStr, timeStr);
    }
    
    // 显示日期时间选择器
    function showDateTimePicker() {
      document.getElementById('datetimePopup').classList.add('show');
    }
    
    // 隐藏日期时间选择器
    function hideDateTimePicker() {
      document.getElementById('datetimePopup').classList.remove('show');
    }
    
    // 确认日期时间选择
    function confirmDateTime() {
      const dateValue = document.getElementById('dateInput').value;
      const timeValue = document.getElementById('timeInput').value;
      
      if (dateValue && timeValue) {
        updateDisplay(dateValue, timeValue);
        hideDateTimePicker();
      } else {
        alert('请选择完整的日期和时间');
      }
    }
    
    // 更新显示
    function updateDisplay(dateStr, timeStr) {
      const today = new Date().toISOString().split('T')[0];
      const isToday = dateStr === today;
      
      let displayText;
      if (isToday) {
        displayText = `今天 ${timeStr}`;
      } else {
        const date = new Date(dateStr);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        displayText = `${month}-${day} ${timeStr}`;
      }
      
      document.getElementById('currentDateTime').textContent = displayText;
    }
    
    // 点击弹窗背景关闭
    document.getElementById('datetimePopup').addEventListener('click', function(e) {
      if (e.target === this) {
        hideDateTimePicker();
      }
    });
    
    // 初始化
    initCurrentTime();
  </script>
</body>
</html>
